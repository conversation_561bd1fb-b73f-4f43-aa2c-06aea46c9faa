package com.android.pigeonringfwupdatetool;

public class AFPCommand {

    /**
     * CheckSum Function
     */
    public static byte Checksum(byte[] data) {
        byte btmp = 0x00;
        for (int i = 1; i < data.length; i++) {
            btmp += data[i];
        }
        return btmp;
    }

    /**
     * Check readdata header and checksum
     */
    public static boolean AnalyzeReceivedata(byte[] data, byte deivceId, byte sourceId, byte squence, int opcode) {
        if (data.length > 8) {
            int u16tmp = (int) (((int) data[6] * 256) + data[7]);
            if (data[0] == 0xff && data[3] == deivceId &&
                    data[4] == sourceId && u16tmp == opcode) {
                byte cs = Checksum(data);
                cs = (byte) (cs - data[data[1] + 1]);
                if (cs == data[data[1] + 1])
                    return true;
            }
        }
        return false;
    }

    /**
     * Convert byte array to hex string
     */
    public static String bytesToHex(byte[] bytes, int length) {
        StringBuilder result = new StringBuilder();
        for (int i = 0; i < length && i < bytes.length; i++) {
            result.append(String.format("%02X ", bytes[i] & 0xFF));
        }
        return result.toString().trim();
    }

    /**
     * Convert single byte to hex string
     */
    public static String byteToHex(byte b) {
        return String.format("%02X", b & 0xFF);
    }
}
