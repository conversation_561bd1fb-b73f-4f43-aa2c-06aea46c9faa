package com.android.pigeonringfwupdatetool;

import java.util.List;

/**
 * Update Sub Data
 * Contains firmware data block information for memory programming
 */
public class UpdateSubData {
    
    /**
     * Memory address offset where this data should be written
     */
    public long addressoffect;
    
    /**
     * Binary data to be written to memory
     */
    public List<Byte> bindatas;
    
    /**
     * Default constructor
     */
    public UpdateSubData() {
        // Default constructor
    }
    
    /**
     * Constructor with parameters
     */
    public UpdateSubData(long addressoffect, List<Byte> bindatas) {
        this.addressoffect = addressoffect;
        this.bindatas = bindatas;
    }
    
    /**
     * Get data size
     */
    public int getDataSize() {
        return bindatas != null ? bindatas.size() : 0;
    }
    
    /**
     * Get data as byte array
     */
    public byte[] getDataArray() {
        if (bindatas == null) {
            return new byte[0];
        }
        
        byte[] result = new byte[bindatas.size()];
        for (int i = 0; i < bindatas.size(); i++) {
            result[i] = bindatas.get(i);
        }
        return result;
    }
    
    @Override
    public String toString() {
        return "UpdateSubData{" +
                "address=0x" + Long.toHexString(addressoffect) +
                ", dataSize=" + getDataSize() +
                '}';
    }
}