package com.android.pigeonringfwupdatetool;

import java.util.UUID;

/**
 * SGN-168 GATT Attributes
 * Contains all the UUIDs and constants for communicating with SGN-168 device
 */
public class SGN168GattAttributes {
    
    // SGN-168 GATT Service and Characteristics
    
    /**
     * Communication Service
     * Brief UUID: 0xFFF0
     */
    public static final UUID GATT_SERVICE = UUID.fromString("0000fff0-0000-1000-8000-00805f9b34fb");
    
    /**
     * Read Characteristic of the service 0xFFF0
     * Brief UUID: 0xFFF4
     */
    public static final UUID READ_CHARACTERISTIC = UUID.fromString("0000fff4-0000-1000-8000-00805f9b34fb");
    
    /**
     * Write Characteristic of the service 0xFFF0
     * Brief UUID: 0xFFF1
     */
    public static final UUID WRITE_CHARACTERISTIC = UUID.fromString("0000fff1-0000-1000-8000-00805f9b34fb");
    
    // Additional constants for firmware update
    
    /**
     * Maximum packet size for firmware data transmission
     */
    public static final int MAX_PACKET_SIZE = 20; // BLE standard MTU size
    
    /**
     * Delay between packets (milliseconds)
     */
    public static final int PACKET_DELAY_MS = 50;
    
    /**
     * Firmware update timeout (milliseconds)
     */
    public static final int UPDATE_TIMEOUT_MS = 30000; // 30 seconds
    
    /**
     * Response timeout for read/write operations (milliseconds)
     */
    public static final int RESPONSE_TIMEOUT_MS = 5000; // 5 seconds
    
    // Command constants (you can add specific commands here)
    
    /**
     * Command to start firmware update
     */
    public static final byte[] CMD_START_UPDATE = {(byte) 0xAA, (byte) 0x01};
    
    /**
     * Command to end firmware update
     */
    public static final byte[] CMD_END_UPDATE = {(byte) 0xAA, (byte) 0x02};
    
    /**
     * Command to read firmware version
     */
    public static final byte[] CMD_READ_VERSION = {(byte) 0xAA, (byte) 0x03};
    
    /**
     * Response success indicator
     */
    public static final byte RESPONSE_SUCCESS = (byte) 0x00;
    
    /**
     * Response error indicator
     */
    public static final byte RESPONSE_ERROR = (byte) 0xFF;
    
    /**
     * Get service name by UUID
     */
    public static String getServiceName(UUID uuid) {
        if (GATT_SERVICE.equals(uuid)) {
            return "SGN-168 Communication Service";
        }
        return "Unknown Service";
    }
    
    /**
     * Get characteristic name by UUID
     */
    public static String getCharacteristicName(UUID uuid) {
        if (READ_CHARACTERISTIC.equals(uuid)) {
            return "SGN-168 Read Characteristic";
        } else if (WRITE_CHARACTERISTIC.equals(uuid)) {
            return "SGN-168 Write Characteristic";
        }
        return "Unknown Characteristic";
    }
    
    /**
     * Check if UUID is SGN-168 service
     */
    public static boolean isSGN168Service(UUID uuid) {
        return GATT_SERVICE.equals(uuid);
    }
    
    /**
     * Check if UUID is SGN-168 read characteristic
     */
    public static boolean isSGN168ReadCharacteristic(UUID uuid) {
        return READ_CHARACTERISTIC.equals(uuid);
    }
    
    /**
     * Check if UUID is SGN-168 write characteristic
     */
    public static boolean isSGN168WriteCharacteristic(UUID uuid) {
        return WRITE_CHARACTERISTIC.equals(uuid);
    }
}