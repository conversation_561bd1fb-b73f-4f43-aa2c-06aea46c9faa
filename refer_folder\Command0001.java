package com.android.flightplanapp.cmd;

import static com.android.flightplanapp.cmd.AFPCommand.AnalyzeReceivedata;
import static com.android.flightplanapp.cmd.AFPCommand.Checksum;

import android.util.Log;

import org.apache.commons.lang3.ArrayUtils;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;

public class Command0001 extends CommandBase {
    public static boolean CheckResponseData(byte[] send, byte[] responce) {
        short Opcode = (short) (((int) send[6]) * 256 + send[7] + 0x8000);
        if (AnalyzeReceivedata(responce, send[4], send[3], send[5], Opcode) == false) {
            return false;
        }
        return true;
    }

    public Command0001(byte devicedid, byte src) {
        header[0] = (byte) 0xff;    //SYNC
        header[1] = (byte) 0x00;    //Message Length
        header[2] = (byte) 0x07;    //Flag
        header[3] = (byte) 0x01;    //Source ID
        header[4] = devicedid;      //Destination ID
        header[5] = src;            //SquenceNumber
        header[6] = (byte) 0x00;    //Opcode
        header[7] = (byte) 0x01;
    }

    byte[] request = new byte[] {0x00, 0x00, 0x00, 0x00};

    public void ClearAllRequest() {
        request[0] = 0;
        request[1] = 0;
        request[2] = 0;
        request[3] = 0;
    }

    public void SetRequestFirmwareCheck(boolean status) {
        if (status)
            request[0] = (byte) (request[0] | 0x01);
        else
            request[0] = (byte) (request[0] & 0xfe);
    }

    public void SetRequestJumpToApp(boolean status) {
        if (status)
            request[1] = (byte) (request[1] | 0x02);
        else
            request[1] = (byte) (request[1] & 0xfd);
    }

    public void SetRequestJumpToBootloader(boolean status) {
        if (status)
            request[1] = (byte) (request[1] | 0x01);
        else
            request[1] = (byte) (request[1] & 0xfe);
    }

    public void SetQueryQueryFirmwareVersion(boolean status) {
        if (status)
            request[3] = (byte) (request[3] | 0x04);
        else
            request[3] = (byte) (request[3] & 0xfb);
    }

    public void SetQueryFirmwareChecksumStatus(boolean status) {
        if (status)
            request[3] = (byte) (request[3] | 0x02);
        else
            request[3] = (byte) (request[3] & 0xfd);
    }

    public void SetRequestOpmode(boolean status) {
        if (status)
            request[3] = (byte) (request[3] | 0x01);
        else
            request[3] = (byte) (request[3] & 0xfe);
    }

    public byte[] GetSendData() {
        ArrayList<Byte> pollingData = new ArrayList<>();
//        header[5] = (byte) squence.nextInt(256);  // Assuming squence is a Random object
        pollingData.addAll(Arrays.asList(ArrayUtils.toObject(header)));
        // pollingData.add(fieldcontrol);
        pollingData.addAll(Arrays.asList(ArrayUtils.toObject(request)));
        pollingData.set(1, (byte) (pollingData.size() - 1));
        pollingData.add(Checksum(listToByte(pollingData)));
        return listToByte(pollingData);
    }

    public static boolean GetOpMode(byte[] data) {
        byte OpMode = (byte) 0xff;
        try {
            final byte REQUEST_LL = 11;
            if ((data[REQUEST_LL] & 0x01) != 0) {
                OpMode = data[12];
                Log.e("LeDa", "OpMode : " + OpMode);
                return true;
            }
            return false;
        } catch (Exception e) {
            return false;
        }
    }

    public static boolean GetApplicationFirmwareCheckFlag(byte[] data, boolean flag) {
        flag = false;
        if (data.length >= 12) {
            if ((data[8] & 0x01) == 0)
                flag = false;
            else
                flag = true;
            return true;
        }
        return false;
    }

    public static boolean GetFirmwareCheckStatus(byte[] data, byte status) {
        status = (byte) 0xff;
        try {
            final byte REQUEST_LL = 11;
            if ((data[REQUEST_LL] & 0x02) != 0) {
                if ((data[REQUEST_LL] & 0x01) == 0) {
                    status = data[12];
                } else {
                    status = data[13];
                }
                return true;
            }
            return false;
        } catch (Exception e){
            return false;
        }
    }

    public static boolean GetFirmwareVersion(byte[] data) {
        String version = "";
        try {
            final byte REQUEST_LL = 11;
            int dataoffect = 12;
            if ((data[REQUEST_LL] & 0x04) != 0) {
                if ((data[REQUEST_LL] & 0x01) != 0)
                    dataoffect += 1;
                if ((data[REQUEST_LL] & 0x02) != 0)
                    dataoffect += 1;
                int length = data[dataoffect];
                dataoffect++;
                byte[] verbyte = new byte[length];
                for (int i = 0; i < length; i++)
                    verbyte[i] = data[dataoffect++];
                version = new String(verbyte, StandardCharsets.UTF_8);
                return true;
            }
            return false;
        } catch (Exception e){
            return false;
        }
    }

    public static String GetFirmwareVersionToStr(byte[] data) {
        String version = "";
        try {
            final byte REQUEST_LL = 11;
            int dataoffect = 12;
            if ((data[REQUEST_LL] & 0x04) != 0) {
                if ((data[REQUEST_LL] & 0x01) != 0)
                    dataoffect += 1;
                if ((data[REQUEST_LL] & 0x02) != 0)
                    dataoffect += 1;
                int length = data[dataoffect];
                dataoffect++;
                byte[] verbyte = new byte[length];
                for (int i = 0; i < length; i++)
                    verbyte[i] = data[dataoffect++];
                version = new String(verbyte, StandardCharsets.UTF_8);
                Log.d("LeDa", "version : " + version);
                return version;
            }
            return "null";
        } catch (Exception e){
            Log.d("LeDa", "Exception : " + e);
            return "null";
        }
    }
}