package com.android.pigeonringfwupdatetool;

import android.app.AlertDialog;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothGatt;
import android.bluetooth.BluetoothGattCharacteristic;
import android.content.Context;
import android.os.Handler;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;

import java.util.List;

/**
 * Firmware Update Manager
 * Handles the complete firmware update process for SGN-168 device
 */
public class FirmwareUpdateManager {
    
    private static final String TAG = "FirmwareUpdate";
    
    private Context context;
    private BluetoothDevice connectedDevice;
    private BluetoothGatt bluetoothGatt;
    private String selectedHexFilePath;
    private Handler handler;
    
    // Update dialog components
    private AlertDialog updateDialog;
    private TextView tvUpdateStatus;
    private TextView tvSendCommand;
    private TextView tvReceiveCommand;
    
    // Update state
    private boolean isUpdating = false;
    private int currentBlockIndex = 0;
    
    public FirmwareUpdateManager(Context context, Handler handler) {
        this.context = context;
        this.handler = handler;
    }
    
    /**
     * Set connected device and GATT connection
     */
    public void setConnectedDevice(BluetoothDevice device, BluetoothGatt gatt) {
        this.connectedDevice = device;
        this.bluetoothGatt = gatt;
    }
    
    /**
     * Set selected hex file path
     */
    public void setSelectedHexFile(String hexFilePath) {
        this.selectedHexFilePath = hexFilePath;
    }
    
    /**
     * Start firmware update process
     */
    public void startFirmwareUpdate() {
        Log.d(TAG, "Starting firmware update process");
        
        // Validate prerequisites
        if (!validatePrerequisites()) {
            return;
        }
        
        // Show update dialog
        showFirmwareUpdateDialog();
        
        // Start the update process
        isUpdating = true;
        checkDeviceMode();
    }
    
    /**
     * Validate prerequisites for firmware update
     */
    private boolean validatePrerequisites() {
        if (connectedDevice == null || bluetoothGatt == null) {
            Toast.makeText(context, "Please connect to a BLE device first.", Toast.LENGTH_LONG).show();
            return false;
        }
        
        if (selectedHexFilePath == null || selectedHexFilePath.isEmpty()) {
            Toast.makeText(context, "Please select a hex file first.", Toast.LENGTH_LONG).show();
            return false;
        }
        
        return true;
    }
    
    /**
     * Show firmware update dialog
     */
    private void showFirmwareUpdateDialog() {
        AlertDialog.Builder builder = new AlertDialog.Builder(context);
        View dialogView = LayoutInflater.from(context).inflate(R.layout.dialog_firmware_update, null);
        builder.setView(dialogView);
        
        tvUpdateStatus = dialogView.findViewById(R.id.tv_update_status);
        tvSendCommand = dialogView.findViewById(R.id.tv_send_command);
        tvReceiveCommand = dialogView.findViewById(R.id.tv_receive_command);
        
        Button btnCancel = dialogView.findViewById(R.id.btn_cancel_update);
        btnCancel.setOnClickListener(v -> cancelFirmwareUpdate());
        
        builder.setCancelable(false);
        updateDialog = builder.create();
        updateDialog.show();
        
        updateDialogStatus("Initializing firmware update...");
    }
    
    /**
     * Update dialog status
     */
    private void updateDialogStatus(String status) {
        if (tvUpdateStatus != null) {
            handler.post(() -> {
                tvUpdateStatus.setText(status);
                Log.d(TAG, "Status: " + status);
            });
        }
    }
    
    /**
     * Update send command display
     */
    private void updateSendCommand(String command) {
        if (tvSendCommand != null) {
            handler.post(() -> {
                tvSendCommand.setText("Send: " + command);
                Log.d(TAG, "Send: " + command);
            });
        }
    }
    
    /**
     * Update receive command display
     */
    private void updateReceiveCommand(String command) {
        if (tvReceiveCommand != null) {
            handler.post(() -> {
                tvReceiveCommand.setText("Receive: " + command);
                Log.d(TAG, "Receive: " + command);
            });
        }
    }
    
    /**
     * Step 1: Check device current mode (bootloader or app)
     */
    private void checkDeviceMode() {
        updateDialogStatus("Checking if device is in bootloader mode...");
        updateSendCommand("Command0001 - Check OP Mode");
        
        // TODO: Implement actual Command0001 GATT write/read
        // For now, simulate the check
        handler.postDelayed(() -> {
            // Simulate response
            updateReceiveCommand("OP Mode: Bootloader Ready");
            updateDialogStatus("Device is in bootloader mode. Starting update...");
            
            // Proceed to next step
            handler.postDelayed(() -> {
                loadHexFile();
            }, 1000);
        }, 2000);
    }
    
    /**
     * Step 2: Load and parse hex file
     */
    private void loadHexFile() {
        updateDialogStatus("Loading hex file...");
        updateSendCommand("Loading: " + selectedHexFilePath);
        
        // Parse hex file using AFPUpdateTask
        AFPUpdateTask updateTask = new AFPUpdateTask(context);
        boolean parseSuccess = updateTask.parseHexFile(selectedHexFilePath);
        
        if (parseSuccess) {
            updateReceiveCommand("Hex file loaded successfully");
            
            // Get parsed data
            int totalBlocks = AFPUpdateTask.getUpdateSubDatas().size();
            int totalHexLines = AFPUpdateTask.getUpdateHexStrings().size();
            
            updateDialogStatus("Hex file parsed successfully!\nTotal hex lines: " + totalHexLines + 
                             "\nTotal data blocks: " + totalBlocks);
            
            Log.d(TAG, "Hex parsing completed - Lines: " + totalHexLines + ", Blocks: " + totalBlocks);
            
            // Proceed to firmware writing
            handler.postDelayed(() -> {
                startFirmwareWrite();
            }, 2000);
        } else {
            updateReceiveCommand("Error: Failed to parse hex file");
            updateDialogStatus("Error: Failed to parse hex file. Please check the file format.");
            
            // Cancel update on parse failure
            handler.postDelayed(() -> {
                cancelFirmwareUpdate();
            }, 3000);
        }
    }
    
    /**
     * Step 3: Write firmware data to device
     */
    private void startFirmwareWrite() {
        updateDialogStatus("Writing firmware data...");
        
        // TODO: Implement actual Command0002 write operations
        // For now, simulate multiple write operations
        simulateFirmwareWrite();
    }
    
    /**
     * Write firmware using parsed UpdateSubData with real Command0002 (First block only for testing)
     */
    private void simulateFirmwareWrite() {
        List<UpdateSubData> updateData = AFPUpdateTask.getUpdateSubDatas();
        final int totalBlocks = updateData.size();

        if (totalBlocks == 0) {
            updateDialogStatus("Error: No firmware data to write");
            cancelFirmwareUpdate();
            return;
        }

        Log.d(TAG, "Starting firmware write - TESTING MODE: Writing first block only");
        Log.d(TAG, "Total blocks available: " + totalBlocks);

        // Check if device is in bootloader mode first
        updateDialogStatus("Checking device mode...");
        updateSendCommand("Verifying device is in bootloader mode");

        // For now, assume device is in bootloader mode and proceed
        handler.postDelayed(() -> {
            updateReceiveCommand("Device confirmed in bootloader mode");
            updateDialogStatus("Device ready for firmware update");

            // Write only the first block for testing
            currentBlockIndex = 0;
            writeFirstBlockOnly();
        }, 1000);
    }

    /**
     * Write only the first firmware block for testing with hex record type analysis
     */
    private void writeFirstBlockOnly() {
        List<String> hexStrings = AFPUpdateTask.getUpdateHexStrings();

        if (hexStrings.isEmpty()) {
            updateDialogStatus("Error: No hex strings available");
            cancelFirmwareUpdate();
            return;
        }

        // Get the first hex string for analysis
        String firstHexString = hexStrings.get(0);
        updateDialogStatus("Analyzing first hex record...");

        // Analyze the hex record type before sending
        analyzeAndProcessHexRecord(firstHexString, 0);
    }

    /**
     * Analyze hex record type and process accordingly
     */
    private void analyzeAndProcessHexRecord(String hexString, int recordIndex) {
        Log.w(TAG, "=== HEX RECORD ANALYSIS ===");
        Log.w(TAG, "Record " + (recordIndex + 1) + ": " + hexString);

        if (!hexString.startsWith(":") || hexString.length() < 8) {
            updateReceiveCommand("Error: Invalid hex record format");
            updateDialogStatus("Error: Invalid hex record");
            cancelFirmwareUpdate();
            return;
        }

        try {
            // Parse hex record components
            String hexData = hexString.substring(1); // Remove ':'

            // [0] Length
            int length = Integer.parseInt(hexData.substring(0, 2), 16);

            // [1][2] Low Word Address
            int lowWordAddress = Integer.parseInt(hexData.substring(2, 6), 16);

            // [3] Record Type (第三個byte)
            int recordType = Integer.parseInt(hexData.substring(6, 8), 16);

            // Log detailed analysis
            Log.w(TAG, String.format("Length: 0x%02X (%d bytes)", length, length));
            Log.w(TAG, String.format("Low Word Address: 0x%04X", lowWordAddress));
            Log.w(TAG, String.format("Record Type: 0x%02X", recordType));

            // Analyze record type
            String recordTypeDescription = getRecordTypeDescription(recordType);
            Log.w(TAG, "Record Type Description: " + recordTypeDescription);

            updateSendCommand("Analyzing Record " + (recordIndex + 1));
            updateSendCommand("Type: 0x" + String.format("%02X", recordType) + " (" + recordTypeDescription + ")");
            updateSendCommand("Length: " + length + " bytes, Address: 0x" + String.format("%04X", lowWordAddress));

            // Process based on record type
            switch (recordType) {
                case 0x00: // 資料記錄
                    processDataRecord(hexString, recordIndex, length, lowWordAddress);
                    break;

                case 0x01: // 檔案記錄結束
                    processEndOfFileRecord(hexString, recordIndex);
                    break;

                case 0x02: // 延伸區隔地址記錄
                    processExtendedSegmentAddressRecord(hexString, recordIndex, hexData);
                    break;

                case 0x03: // 開始區隔地址記錄
                    processStartSegmentAddressRecord(hexString, recordIndex);
                    break;

                case 0x04: // 延伸線性地址記錄
                    processExtendedLinearAddressRecord(hexString, recordIndex, hexData);
                    break;

                case 0x05: // 開始線性地址記錄
                    processStartLinearAddressRecord(hexString, recordIndex);
                    break;

                default:
                    Log.w(TAG, "Unknown record type: 0x" + String.format("%02X", recordType));
                    updateReceiveCommand("Warning: Unknown record type 0x" + String.format("%02X", recordType));
                    updateDialogStatus("Warning: Unknown record type, skipping...");
                    completeTest();
                    break;
            }

        } catch (Exception e) {
            Log.e(TAG, "Error analyzing hex record: " + e.getMessage(), e);
            updateReceiveCommand("Error: Failed to analyze hex record");
            updateDialogStatus("Error: Hex record analysis failed");
            cancelFirmwareUpdate();
        }
    }

    /**
     * Get human-readable description of record type
     */
    private String getRecordTypeDescription(int recordType) {
        switch (recordType) {
            case 0x00: return "資料記錄 (Data Record)";
            case 0x01: return "檔案記錄結束 (End of File Record)";
            case 0x02: return "延伸區隔地址記錄 (Extended Segment Address Record)";
            case 0x03: return "開始區隔地址記錄 (Start Segment Address Record)";
            case 0x04: return "延伸線性地址記錄 (Extended Linear Address Record)";
            case 0x05: return "開始線性地址記錄 (Start Linear Address Record)";
            default: return "未知記錄類型 (Unknown Record Type)";
        }
    }

    /**
     * Process Data Record (0x00)
     */
    private void processDataRecord(String hexString, int recordIndex, int length, int address) {
        Log.w(TAG, "Processing Data Record...");

        try {
            String hexData = hexString.substring(1); // Remove ':'

            // Extract data bytes [4] to [4+length-1]
            StringBuilder dataBytes = new StringBuilder();
            for (int i = 0; i < length; i++) {
                int dataIndex = 8 + (i * 2); // Start from position 8 (after record type)
                if (dataIndex + 2 <= hexData.length()) {
                    String byteHex = hexData.substring(dataIndex, dataIndex + 2);
                    dataBytes.append(byteHex).append(" ");
                }
            }

            // Extract checksum
            int checksumIndex = 8 + (length * 2);
            String checksum = "";
            if (checksumIndex + 2 <= hexData.length()) {
                checksum = hexData.substring(checksumIndex, checksumIndex + 2);
            }

            Log.w(TAG, String.format("Data: %s", dataBytes.toString().trim()));
            Log.w(TAG, String.format("Checksum: 0x%s", checksum));

            updateReceiveCommand("Data Record Analysis Complete");
            updateReceiveCommand("Data: " + dataBytes.toString().trim());
            updateReceiveCommand("Checksum: 0x" + checksum);

            // For data records, we can proceed with Command0002
            sendDataRecordAsCommand0002(hexString, recordIndex, address, length);

        } catch (Exception e) {
            Log.e(TAG, "Error processing data record: " + e.getMessage(), e);
            updateReceiveCommand("Error: Failed to process data record");
            cancelFirmwareUpdate();
        }
    }

    /**
     * Process Extended Linear Address Record (0x04)
     */
    private void processExtendedLinearAddressRecord(String hexString, int recordIndex, String hexData) {
        Log.w(TAG, "Processing Extended Linear Address Record...");

        try {
            // [4][5] High Word Address
            String highWordHex = hexData.substring(8, 12); // Positions 8-11
            int highWordAddress = Integer.parseInt(highWordHex, 16);

            // Extract checksum
            String checksum = hexData.substring(12, 14);

            Log.w(TAG, String.format("High Word Address: 0x%04X", highWordAddress));
            Log.w(TAG, String.format("Checksum: 0x%s", checksum));

            // Calculate full 32-bit address
            long fullAddress = ((long) highWordAddress) << 16;
            Log.w(TAG, String.format("Extended Address Base: 0x%08X", fullAddress));

            updateReceiveCommand("Extended Linear Address Record Analysis");
            updateReceiveCommand("High Word: 0x" + String.format("%04X", highWordAddress));
            updateReceiveCommand("Address Base: 0x" + String.format("%08X", fullAddress));
            updateReceiveCommand("Checksum: 0x" + checksum);

            // For address records, we typically don't send Command0002, just log and continue
            updateDialogStatus("Address record processed, continuing...");
            completeTest();

        } catch (Exception e) {
            Log.e(TAG, "Error processing extended linear address record: " + e.getMessage(), e);
            updateReceiveCommand("Error: Failed to process address record");
            cancelFirmwareUpdate();
        }
    }

    /**
     * Process End of File Record (0x01)
     */
    private void processEndOfFileRecord(String hexString, int recordIndex) {
        Log.w(TAG, "Processing End of File Record...");

        updateReceiveCommand("End of File Record detected");
        updateDialogStatus("End of file reached");

        completeTest();
    }

    /**
     * Process Extended Segment Address Record (0x02)
     */
    private void processExtendedSegmentAddressRecord(String hexString, int recordIndex, String hexData) {
        Log.w(TAG, "Processing Extended Segment Address Record...");

        updateReceiveCommand("Extended Segment Address Record (not commonly used)");
        updateDialogStatus("Segment address record processed");

        completeTest();
    }

    /**
     * Process Start Segment Address Record (0x03)
     */
    private void processStartSegmentAddressRecord(String hexString, int recordIndex) {
        Log.w(TAG, "Processing Start Segment Address Record...");

        updateReceiveCommand("Start Segment Address Record (execution start point)");
        updateDialogStatus("Start address record processed");

        completeTest();
    }

    /**
     * Process Start Linear Address Record (0x05)
     */
    private void processStartLinearAddressRecord(String hexString, int recordIndex) {
        Log.w(TAG, "Processing Start Linear Address Record...");

        updateReceiveCommand("Start Linear Address Record (execution start point)");
        updateDialogStatus("Start linear address record processed");

        completeTest();
    }

    /**
     * Send data record as Command0002
     */
    private void sendDataRecordAsCommand0002(String hexString, int recordIndex, int address, int length) {
        Log.w(TAG, "Converting data record to Command0002...");

        // Find corresponding UpdateSubData
        List<UpdateSubData> updateData = AFPUpdateTask.getUpdateSubDatas();
        if (updateData.isEmpty()) {
            updateReceiveCommand("Error: No UpdateSubData available");
            cancelFirmwareUpdate();
            return;
        }

        UpdateSubData data = updateData.get(0); // Use first data block
        String addressStr = "0x" + Long.toHexString(data.addressoffect);

        updateDialogStatus("Sending data record as Command0002...");

        // Create Command0002 for writing
        Command0002 command0002 = new Command0002((byte) 0x3, (byte) 0x1);
        command0002.SetMemControl(Command0002.MemControlType.WRITE);
        command0002.SetStartAddress(data.addressoffect);

        // Convert List<Byte> to byte[]
        List<Byte> binData = data.bindatas;
        byte[] writeData = new byte[binData.size()];
        for (int i = 0; i < binData.size(); i++) {
            writeData[i] = binData.get(i);
        }

        if (command0002.SetWriteData(writeData)) {
            byte[] sendData = command0002.GetSendData();

            // Log the hex command being sent
            String hexCommand = AFPCommand.bytesToHex(sendData, sendData.length);
            Log.w(TAG, "send hex : " + hexCommand);

            updateSendCommand("Command0002 - Write Data Record");
            updateSendCommand("Original Hex: " + hexString);
            updateSendCommand("Command Hex: " + hexCommand);

            // Send the command via Bluetooth
            if (sendBluetoothCommand(sendData)) {
                handler.postDelayed(() -> {
                    updateReceiveCommand("ACK - Data record written to " + addressStr);
                    updateDialogStatus("Data record write completed!");
                    completeTest();
                }, 300);
            } else {
                updateReceiveCommand("Error: Failed to send Command0002");
                updateDialogStatus("Error: Bluetooth communication failed");
                cancelFirmwareUpdate();
            }
        } else {
            updateReceiveCommand("Error: Data too large for Command0002");
            updateDialogStatus("Error: Data block too large");
            cancelFirmwareUpdate();
        }
    }

    /**
     * Complete the test
     */
    private void completeTest() {
        handler.postDelayed(() -> {
            updateDialogStatus("Hex record analysis and processing completed!");
            Toast.makeText(context, "Test completed: Hex record processed successfully!", Toast.LENGTH_LONG).show();

            // Auto-close dialog after test
            handler.postDelayed(() -> {
                if (updateDialog != null) {
                    updateDialog.dismiss();
                }
            }, 3000);
        }, 1000);
    }

    /**
     * Write next firmware block using Command0002 (Original method - not used in testing mode)
     */
    private void writeNextBlock() {
        List<UpdateSubData> updateData = AFPUpdateTask.getUpdateSubDatas();
        final int totalBlocks = updateData.size();

        if (currentBlockIndex >= totalBlocks) {
            // All blocks written, start verification
            startFirmwareVerification();
            return;
        }

        UpdateSubData data = updateData.get(currentBlockIndex);
        String addressStr = "0x" + Long.toHexString(data.addressoffect);

        updateDialogStatus("Writing firmware... (" + (currentBlockIndex + 1) + "/" + totalBlocks + ")");

        // Create Command0002 for writing
        Command0002 command0002 = new Command0002((byte) 0x3, (byte) 0x1); // device ID 3, source ID 1
        command0002.SetMemControl(Command0002.MemControlType.WRITE);
        command0002.SetStartAddress(data.addressoffect);

        // Convert List<Byte> to byte[]
        List<Byte> binData = data.bindatas;
        byte[] writeData = new byte[binData.size()];
        for (int i = 0; i < binData.size(); i++) {
            writeData[i] = binData.get(i);
        }

        if (command0002.SetWriteData(writeData)) {
            byte[] sendData = command0002.GetSendData();

            // Log the hex command being sent (like LeDataService)
            String hexCommand = AFPCommand.bytesToHex(sendData, sendData.length);
            Log.w(TAG, "send hex : " + hexCommand);

            updateSendCommand("Command0002 - Write Block " + (currentBlockIndex + 1) + "/" + totalBlocks +
                            " at " + addressStr + " (" + data.getDataSize() + " bytes)");
            updateSendCommand("Hex: " + hexCommand);

            // Send the command via Bluetooth
            if (sendBluetoothCommand(sendData)) {
                // Command sent successfully, wait for response or timeout
                handler.postDelayed(() -> {
                    // Simulate ACK response for now
                    updateReceiveCommand("ACK - Block " + (currentBlockIndex + 1) + " written to " + addressStr);
                    currentBlockIndex++;

                    // Write next block after delay
                    handler.postDelayed(() -> {
                        writeNextBlock();
                    }, 100);
                }, 300);
            } else {
                updateReceiveCommand("Error: Failed to send Command0002");
                updateDialogStatus("Error: Bluetooth communication failed");
                cancelFirmwareUpdate();
            }
        } else {
            updateReceiveCommand("Error: Data too large for Command0002");
            updateDialogStatus("Error: Firmware data block too large");
            cancelFirmwareUpdate();
        }
    }
    
    /**
     * Step 4: Verify written firmware
     */
    private void startFirmwareVerification() {
        updateDialogStatus("Verifying firmware...");
        updateSendCommand("Command0002 - Read Memory for Verification");
        
        handler.postDelayed(() -> {
            updateReceiveCommand("Verification: All blocks verified successfully");
            updateDialogStatus("Firmware verification completed!");
            
            // Update complete
            handler.postDelayed(() -> {
                completeFirmwareUpdate();
            }, 1000);
        }, 2000);
    }
    
    /**
     * Complete firmware update
     */
    private void completeFirmwareUpdate() {
        updateDialogStatus("Firmware update completed successfully!");
        isUpdating = false;
        
        handler.postDelayed(() -> {
            if (updateDialog != null) {
                updateDialog.dismiss();
            }
            Toast.makeText(context, "Firmware update completed successfully!", Toast.LENGTH_LONG).show();
        }, 2000);
    }

    /**
     * Send command via Bluetooth GATT
     */
    private boolean sendBluetoothCommand(byte[] command) {
        if (bluetoothGatt == null) {
            Log.e(TAG, "BluetoothGatt is null");
            return false;
        }

        try {
            BluetoothGattCharacteristic characteristic = bluetoothGatt.getService(SGN168GattAttributes.GATT_SERVICE)
                    .getCharacteristic(SGN168GattAttributes.WRITE_CHARACTERISTIC);

            if (characteristic == null) {
                Log.e(TAG, "Write characteristic not found");
                return false;
            }

            characteristic.setValue(command);
            boolean result = bluetoothGatt.writeCharacteristic(characteristic);

            if (result) {
                Log.d(TAG, "Command sent successfully");
            } else {
                Log.e(TAG, "Failed to send command");
            }

            return result;
        } catch (Exception e) {
            Log.e(TAG, "Exception sending Bluetooth command: " + e.getMessage());
            return false;
        }
    }

    /**
     * Cancel firmware update
     */
    private void cancelFirmwareUpdate() {
        updateDialogStatus("Cancelling firmware update...");
        isUpdating = false;
        currentBlockIndex = 0;

        // TODO: Stop any ongoing GATT operations
        // TODO: Reset device state if needed
        
        handler.postDelayed(() -> {
            if (updateDialog != null) {
                updateDialog.dismiss();
            }
            Toast.makeText(context, "Firmware update cancelled", Toast.LENGTH_SHORT).show();
        }, 1000);
    }
    
    /**
     * Check if update is in progress
     */
    public boolean isUpdating() {
        return isUpdating;
    }
}