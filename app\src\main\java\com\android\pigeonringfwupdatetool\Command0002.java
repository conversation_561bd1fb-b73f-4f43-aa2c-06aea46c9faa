package com.android.pigeonringfwupdatetool;

import android.util.Log;
import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class Command0002 extends CommandBase {

    static String TAG = "Command0002";

    public static boolean CheckResponseData(byte[] send, byte[] recv) {
        short Opcode = (short)(((int)send[6]) * 256 + send[7] + 0x8000);
        if (!AFPCommand.AnalyzeReceivedata(recv, send[4], send[3], send[5], Opcode)) {
            return false;
        }
        return true;
    }

    static int MEMORY_CONTROL_OFFECT = 8;
    static int MEMORY_LENGTH_OFFSET = 13;
    static int MEMORY_DATA_OFFSET = 14;

    private byte memcontrol;
    private long startaddress;
    private byte datalength;
    private byte[] data;

    public Command0002(byte devicedid, byte src) {
        header[0] = (byte) 0xff;    //SYNC
        header[1] = (byte) 0x00;    //Message Length
        header[2] = (byte) 0x07;    //Flag
        header[3] = (byte) 0x01;    //Source ID
        header[4] = devicedid;      //Destination ID
        header[5] = src;            //SquenceNumber
        header[6] = (byte) 0x00;    //Opcode
        header[7] = 0x02;
    }

    public enum MemControlType {
        ERASE(1),
        READ(2),
        WRITE(3),
        APP_INFO(4);

        private final int value;
        MemControlType(int i) {
            this.value = i;
        }

        public int getValue() {
            return value;
        }
    }

    public void SetMemControl(MemControlType memcontrolType) {
        memcontrol = (byte)memcontrolType.getValue();
    }

    public enum MemStructureType {
        EndAddress(1),
        DataLen(2);
        private final int value;
        MemStructureType(int i) {
            this.value = i;
        }

        public int getValue() {
            return value;
        }
    }

    public void SetStartAddress(long startaddress) {
        this.startaddress = startaddress;
    }

    public void SetDataLength(byte lenth) {
        this.datalength = lenth;
    }

    public boolean SetWriteData(byte[] data) {
        if (data.length <= 32) {
            this.data = data;
            return true;
        }
        return false;
    }

    @Override
    public byte[] GetSendData() {
        List<Byte> pollingdata = new ArrayList<Byte>();
        
        // Add header (0~7)
        for (byte b : header) {
            pollingdata.add(b);
        }
        
        // Add memory control (8)
        pollingdata.add(memcontrol);
        
        // Add start address (9~12)
        byte[] bstmp = ByteBuffer.allocate(4).putInt((int) startaddress).array();
        for (int i = 0; i < bstmp.length; i++) {
            pollingdata.add(bstmp[i]);
        }
        
        if (memcontrol == (byte)MemControlType.WRITE.getValue() || memcontrol == (byte)MemControlType.APP_INFO.getValue()) {
            // Add data length
            pollingdata.add((byte)data.length);
            // Add data
            for (byte b : data) {
                pollingdata.add(b);
            }
        }
        
        // Convert to byte array
        byte[] result = listToByte(pollingdata);
        
        // Set message length
        result[1] = (byte)(result.length - 2);
        
        // Calculate and add checksum
        byte checksum = AFPCommand.Checksum(result);
        List<Byte> finalData = new ArrayList<>();
        for (byte b : result) {
            finalData.add(b);
        }
        finalData.add(checksum);
        
        return listToByte(finalData);
    }

    public static boolean GetMemoryData(byte[] data) {
        try {
            // 獲取記憶體數據長度
            int memDataLength = data[MEMORY_LENGTH_OFFSET] & 0xFF; // 無符號轉換
            Log.d(TAG, "CMD: 0002 memDataLength : " + memDataLength + ", return true");
            // 初始化 memData.data 為正確大小
            data = new byte[memDataLength];
            // 複製數據到 memData.data
            //System.arraycopy(來源, 起始索引, 目的, 起始索引, 複製長度)
            System.arraycopy(data, MEMORY_DATA_OFFSET, data, 0, memDataLength);
            return true;
        } catch (Exception e) {
            Log.e(TAG, "GetMemoryData error: " + e.getMessage());
            return false;
        }
    }
}
