package com.android.flightplanapp.cmd;

import static com.android.flightplanapp.cmd.AFPCommand.Checksum;
import java.util.List;
import java.util.Random;

public class CommandBase implements GetSendData {

    protected Random squence = new Random();
    protected byte[] header = new byte[8];

    @Override
    public byte[] GetSendData() {
        return new byte[0];
    }

    // List<Byte> to byte[]
    public static byte[] listToByte(List<Byte> list) {
        byte[] byteArray = new byte[list.size()];
        for (int i = 0; i < list.size(); i++) {
            byteArray[i] = list.get(i);
        }
        return byteArray;
    }

    public enum ResponceStatus
    {
        ACK,
        NAK,
        CSError,
        OpCodeError,
        HeaderError,
        UnknowError
    }
    public static ResponceStatus ResponceAnalyze(byte[] tcidata, byte[] trcpdata)
    {
        if (trcpdata.length > 8)
        {
            if (trcpdata[0] != 0xff || trcpdata[3] != 0x11 || trcpdata[4] != 0x01)
                return ResponceStatus.HeaderError;
            byte cs = Checksum(trcpdata);
            cs = (byte)(cs - trcpdata[trcpdata[1] + 1]);
            if (cs != trcpdata[trcpdata[1] + 1])
                return ResponceStatus.CSError;
            if ((trcpdata[2] & 0x0C) == 0x08)
                return ResponceStatus.NAK;
            int trcpOpcode = (int)(((int)trcpdata[6] * 256) + trcpdata[7]);
            int tciOpcode = (int)(((int)tcidata[6] * 256) + tcidata[7]);
            if ((int)trcpOpcode != (int)(tciOpcode + 0x8000))
                return ResponceStatus.OpCodeError;
            else
                return ResponceStatus.ACK;
        }
        return ResponceStatus.HeaderError;
    }

}
