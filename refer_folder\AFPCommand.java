package com.android.flightplanapp.cmd;

import static com.android.flightplanapp.cmd.AFPCommand.Checksum;

import java.util.Random;

public class AFPCommand {

    /**
     * /// <summary>
     * /// CheckSum Function
     * /// </summary>
     * /// <param name="data"></param>
     * /// <returns></returns>
     */
    public static byte Checksum(byte[] data) {
        byte btmp = 0x00;
        for (int i = 1; i < data.length; i++) {
            btmp += data[i];
        }
        return btmp;
    }

    /**
     * /// <summary>
     * /// check readdata header and checksum
     * /// </summary>
     * /// <param name="data"></param>
     * /// <param name="deivceId"></param>
     * /// <param name="sourceId"></param>
     * /// <param name="squence"></param>
     * /// <param name="opcode"></param>
     * /// <returns></returns>
     */
    //public static bool IsNakRespoonce(byte data)
    //{

    //}
    public static boolean AnalyzeReceivedata(byte[] data, byte deivceId, byte sourceId, byte squence, int opcode) {
        if (data.length > 8) {
            int u16tmp = (int) (((int) data[6] * 256) + data[7]);
            if (data[0] == 0xff && data[3] == deivceId &&
                    data[4] == sourceId && u16tmp == opcode) {
                byte cs = Checksum(data);
                cs = (byte) (cs - data[data[1] + 1]);
                if (cs == data[data[1] + 1])
                    return true;
            }
        }
        return false;
    }

    static public int RequestDataOffect;
    static public int getRequestDataOffset() {
        return RequestDataOffect;
    }
    static public void setRequestDataOffset(int value) {
        RequestDataOffect = value;
    }

    static public byte RequestDatalength;
    static public byte getRequestDatalength() {
        return RequestDatalength;
    }
    static public void setRequestDatalength(byte value) {
        RequestDataOffect = value;
    }

}