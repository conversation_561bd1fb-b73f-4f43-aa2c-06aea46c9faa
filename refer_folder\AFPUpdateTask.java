package com.android.flightplanapp.updateutils;

import static com.android.flightplanapp.bluetooth.LeDataService.updateHexStrings;
import static com.android.flightplanapp.bluetooth.LeDataService.updateSubDatas;

import android.util.Log;

import com.android.flightplanapp.cmd.Command0001;
import com.android.flightplanapp.cmd.Command0002;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStreamReader;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;

public class AFPUpdateTask {

    String TAG = "AFPUpdateTask";
    // for fw update
//    public static String filepath;
//    public static List<String> updateHexStrings = new ArrayList<>();
//    public static List<UpdateSubData> updateSubDatas = new ArrayList<>();

    // 新的讀取方法，使用 InputStreamReader
    private void readHexFile(String filepath) {
        updateHexStrings.clear();
        try (FileInputStream fis = new FileInputStream(filepath);
             InputStreamReader isr = new InputStreamReader(fis, "UTF-8");
             BufferedReader br = new BufferedReader(isr)) {

            String line;
            while ((line = br.readLine()) != null) {
                updateHexStrings.add(line);
            }

            Log.d(TAG, "File read successfully");
        } catch (Exception e) {
            Log.e(TAG, "Error reading file: " + e.getMessage(), e);
        }
    }

    public boolean executeCheckHexFile(String filepath) {
        Log.e(TAG, "executeCheckHexFile");

        File file = new File(filepath);
        if (!file.exists()) {
            Log.d(TAG, "File does not exist");
            return false;
        } else {
            if (file.canRead()) {
                Log.d(TAG, "File is readable");
            } else {
                Log.d(TAG, "File is not readable");
                return false;
            }
        }

        try {
            // 使用新的讀取方法
            readHexFile(filepath);

            boolean endHexTab = false;
            long baseAddress = 0;

            for (int i = 0; i < updateHexStrings.size() && !endHexTab; i++) {
//                Log.e(TAG, i + " >>>>" + updateHexStrings.get(i).charAt(0));
                if (updateHexStrings.get(i).charAt(0) != ':') {
//                    Log.w(TAG, "charAt(0) != ':'");
                    return false;
                }

                int checksum = 0;
                int checkIndex = 1;

                while (checkIndex < updateHexStrings.get(i).length() - 1) {
                    checksum -= Integer.parseInt(updateHexStrings.get(i).substring(checkIndex, checkIndex + 2), 16);
                    checkIndex += 2;
                }

                if ((byte) checksum != 0) {
                    return false;
                }

                int byteLength = Integer.parseInt(updateHexStrings.get(i).substring(1, 3), 16);
                int baseAddressH = Integer.parseInt(updateHexStrings.get(i).substring(3, 5), 16);
                int baseAddressL = Integer.parseInt(updateHexStrings.get(i).substring(5, 7), 16);
                long addressLow = ((long) (baseAddressH << 8)) + ((long) baseAddressL);
                int type = Integer.parseInt(updateHexStrings.get(i).substring(7, 9), 16);

                if (type == 4) {
                    baseAddressH = Integer.parseInt(updateHexStrings.get(i).substring(9, 11), 16);
                    baseAddressL = Integer.parseInt(updateHexStrings.get(i).substring(11, 13), 16);
                    baseAddress = ((long) (baseAddressH << 24)) + ((long) (baseAddressL << 16));
                } else if (type == 0 && (baseAddress + addressLow) >= 0x08004000) {
                    long addressEffect = baseAddress + addressLow;
                    byte[] binData = new byte[byteLength];
                    checkIndex = 9;

                    for (int j = 0; j < binData.length; j++) {
                        binData[j] = (byte) Integer.parseInt(updateHexStrings.get(i).substring(checkIndex, checkIndex + 2), 16);
                        checkIndex += 2;
                    }

                    long dataCount = 0;
                    while (dataCount < binData.length) {
                        if ((addressEffect % 32 + dataCount) >= 32) {
                            addressEffect += dataCount;
                        }

                        long listIndex = (addressEffect - 0x08004000) / 32;
                        while (listIndex >= updateSubDatas.size()) {
                            UpdateSubData subDataTmp = new UpdateSubData(
                                    updateSubDatas.size() * 32L, // 計算 addressEffect
                                    new ArrayList<>()            // 初始化為空的 bindatas
                            );
                            updateSubDatas.add(subDataTmp);
                        }

                        while (updateSubDatas.get((int) listIndex).bindatas.size() <= (addressEffect % 32 + dataCount)) {
                            updateSubDatas.get((int) listIndex).bindatas.add((byte) 0);
                        }

                        updateSubDatas.get((int) listIndex).bindatas.set((int) (addressEffect % 32 + dataCount), binData[(int) dataCount]);
                        dataCount++;
                    }
                } else if (type == 1) {
                    endHexTab = true;
                }
            }

            Log.e(TAG,"EndHexTab: " + endHexTab);
            return endHexTab;
        } catch (Exception e) {
            Log.e(TAG, "Exception : " + e);
            return false;
        }
    }
}