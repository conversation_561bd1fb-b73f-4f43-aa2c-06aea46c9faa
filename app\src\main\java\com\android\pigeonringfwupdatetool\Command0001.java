package com.android.pigeonringfwupdatetool;

import android.util.Log;
import java.util.ArrayList;
import java.util.List;

public class Command0001 extends CommandBase {
    
    static String TAG = "Command0001";
    
    public static boolean CheckResponseData(byte[] send, byte[] response) {
        short Opcode = (short)(((int)send[6]) * 256 + send[7] + 0x8000);
        if (!AFPCommand.AnalyzeReceivedata(response, send[4], send[3], send[5], Opcode)) {
            return false;
        }
        return true;
    }
    
    private byte[] request = new byte[] {0x00, 0x00, 0x00, 0x00};
    
    public Command0001(byte deviceId, byte src) {
        header[0] = (byte) 0xff;    //SYNC
        header[1] = (byte) 0x00;    //Message Length
        header[2] = (byte) 0x07;    //Flag
        header[3] = (byte) 0x01;    //Source ID
        header[4] = deviceId;       //Destination ID
        header[5] = src;            //SequenceNumber
        header[6] = (byte) 0x00;    //Opcode Low
        header[7] = (byte) 0x01;    //Opcode High
    }
    
    public void ClearAllRequest() {
        request[0] = 0;
        request[1] = 0;
        request[2] = 0;
        request[3] = 0;
    }
    
    public void SetRequestFirmwareCheck(boolean status) {
        if (status)
            request[0] = (byte) (request[0] | 0x01);
        else
            request[0] = (byte) (request[0] & 0xfe);
    }
    
    public void SetRequestJumpToApp(boolean status) {
        if (status)
            request[1] = (byte) (request[1] | 0x02);
        else
            request[1] = (byte) (request[1] & 0xfd);
    }
    
    public void SetRequestJumpToBootloader(boolean status) {
        if (status)
            request[1] = (byte) (request[1] | 0x01);
        else
            request[1] = (byte) (request[1] & 0xfe);
    }
    
    public void SetQueryQueryFirmwareVersion(boolean status) {
        if (status)
            request[3] = (byte) (request[3] | 0x04);
        else
            request[3] = (byte) (request[3] & 0xfb);
    }
    
    public void SetRequestOpmode(boolean status) {
        if (status)
            request[3] = (byte) (request[3] | 0x01);
        else
            request[3] = (byte) (request[3] & 0xfe);
    }
    
    @Override
    public byte[] GetSendData() {
        List<Byte> pollingData = new ArrayList<>();
        
        // Add header (0~7)
        for (byte b : header) {
            pollingData.add(b);
        }
        
        // Add request (8~11)
        for (byte b : request) {
            pollingData.add(b);
        }
        
        // Set message length
        pollingData.set(1, (byte) (pollingData.size() - 1));
        
        // Calculate and add checksum
        byte[] result = listToByte(pollingData);
        byte checksum = AFPCommand.Checksum(result);
        pollingData.add(checksum);
        
        return listToByte(pollingData);
    }
    
    public static boolean GetOpMode(byte[] data) {
        try {
            final byte REQUEST_LL = 11;
            if ((data[REQUEST_LL] & 0x01) != 0) {
                byte OpMode = data[12];
                Log.d(TAG, "OpMode : " + OpMode);
                return true;
            }
            return false;
        } catch (Exception e) {
            Log.e(TAG, "Error getting OpMode: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Parse firmware version from response data
     */
    public static String GetFirmwareVersionToStr(byte[] data) {
        try {
            // Check if this is a firmware version response
            if (data.length > 13 && (data[11] & 0x04) != 0) {
                // Extract firmware version string starting from byte 13
                int dataLength = data[1] & 0xFF; // Message length
                int versionStartIndex = 13;
                int versionLength = dataLength - 11; // Total length minus header and request
                
                if (versionStartIndex + versionLength <= data.length) {
                    byte[] versionBytes = new byte[versionLength];
                    System.arraycopy(data, versionStartIndex, versionBytes, 0, versionLength);
                    
                    // Convert to string, removing any null terminators
                    String version = new String(versionBytes).trim();
                    // Remove any non-printable characters
                    version = version.replaceAll("[\\x00-\\x1F\\x7F]", "");
                    
                    Log.d(TAG, "Parsed firmware version: " + version);
                    return version;
                }
            }
            return "Unknown Version";
        } catch (Exception e) {
            Log.e(TAG, "Error parsing firmware version: " + e.getMessage());
            return "Parse Error";
        }
    }
}
