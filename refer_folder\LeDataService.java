package com.android.flightplanapp.bluetooth;

import static com.android.flightplanapp.bluetooth.GattAttributes.GATT_SERVICE;
import static com.android.flightplanapp.bluetooth.GattAttributes.READ_CHARACTERISTIC;
import static com.android.flightplanapp.bluetooth.GattAttributes.WRITE_CHARACTERISTIC;
import static com.android.flightplanapp.cmd.Command0001.GetFirmwareVersionToStr;
import static com.android.flightplanapp.cmd.CommandBase.listToByte;
import static com.android.flightplanapp.device.CommParam.COMPARE_PAYLOAD_LIST;
import static com.android.flightplanapp.device.Config.CMD_0001;
import static com.android.flightplanapp.device.Config.CMD_0011;
import static com.android.flightplanapp.device.Config.CMD_0012;
import static com.android.flightplanapp.device.Config.CMD_0013;
import static com.android.flightplanapp.device.Config.CMD_0081;
import static com.android.flightplanapp.device.Config.CMD_0081_DEL_DATA;
import static com.android.flightplanapp.device.Config.CMD_00FA;
import static com.android.flightplanapp.device.Config.CMD_GET_OP_MODE;
import static com.android.flightplanapp.device.Config.CMD_QUERY_FW_VER;
import static com.android.flightplanapp.device.Config.CMD_SWITCH_APP_MODE;
import static com.android.flightplanapp.device.Config.CMD_SWITCH_BOOTLOADER;
import static com.android.flightplanapp.device.Config.CMD_UPGRADE_FW;
import static com.android.flightplanapp.device.Config.DOWNLOAD_PATH;
import static com.android.flightplanapp.device.Config.HEX_FILE_NAME;
import static com.android.flightplanapp.device.Config.WAIT_REBOOT_TIME;
import static com.android.flightplanapp.utils.CommMethod.getDate;
import static com.android.flightplanapp.utils.CommMethod.readHexFileFromPath;
import static com.android.flightplanapp.utils.CommMethod.showToast;
import static com.android.flightplanapp.utils.Parse.byteToHex;
import static com.android.flightplanapp.utils.Parse.bytesToHex;
import static com.android.flightplanapp.utils.Parse.hexStringToInt;
import static com.android.flightplanapp.utils.Parse.parseChecksum;
import static com.android.flightplanapp.utils.Parse.parseLfid;
import static com.android.flightplanapp.utils.Parse.parseOpCode;
import static com.android.flightplanapp.utils.Parse.parsePayload;
import static com.android.flightplanapp.utils.Parse.parseRingCode;
import static com.android.flightplanapp.utils.Parse.parseYear;
import static java.lang.Thread.sleep;

import android.app.Service;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothGatt;
import android.bluetooth.BluetoothGattCallback;
import android.bluetooth.BluetoothGattCharacteristic;
import android.bluetooth.BluetoothGattDescriptor;
import android.bluetooth.BluetoothGattService;
import android.bluetooth.BluetoothManager;
import android.bluetooth.BluetoothProfile;
import android.content.Context;
import android.content.Intent;
import android.os.Binder;
import android.os.Build;
import android.os.CountDownTimer;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;
import android.util.Log;
import android.widget.Toast;

import androidx.annotation.Nullable;

import com.android.flightplanapp.R;
import com.android.flightplanapp.cmd.Command0001;
import com.android.flightplanapp.cmd.Command0002;
import com.android.flightplanapp.cmd.Command0081;
import com.android.flightplanapp.db.DatabaseHelper;
import com.android.flightplanapp.db.model.FlightData;
import com.android.flightplanapp.db.query;
import com.android.flightplanapp.device.Config;
import com.android.flightplanapp.updateutils.AFPUpdateTask;
import com.android.flightplanapp.updateutils.UpdateSubData;
import com.android.flightplanapp.utils.CommandExecutor;
import com.android.flightplanapp.utils.GeneralMsgEvent;
import com.android.flightplanapp.utils.MessageEvent;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStreamReader;
import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class LeDataService extends Service {
    private final String TAG = "LeDataService";
    private BluetoothManager mBluetoothManager;
    private BluetoothAdapter mBluetoothAdapter;
    private String mBluetoothDeviceAddress;
    private BluetoothGatt mBluetoothGatt;
    private int mConnectionState = STATE_DISCONNECTED;
    private static final int STATE_DISCONNECTED = 0;
    private static final int STATE_CONNECTING = 1;
    private static final int STATE_CONNECTED = 2;
    private EventBus eventBus = EventBus.getDefault();
    private BluetoothConnectionListener mConnectionListener;
    /**
     * For command 0081
     */
    private boolean isCmd0081 = false;
    public static int DATAOFFECT = 0;
    public static int TARGET_MSG_LENGTH;
    public static String CHECK_OPCODE;
    boolean IS_START_REQUEST_FLY_DATA = false;
    int TOTAL_FLIGHT_DATA_LEN = 0;
    private int totalExpectedLength = 0; // expect full data length
    int totalReceivedLength = 0;
    private static int SIGNAL_EXPECTED_DATA_LENGTH = 205; // except signal data length
    private ByteBuffer receivedData = ByteBuffer.allocate(1024); // 初始化一個容量為 1024 的 ByteBuffer
    private List<byte[]> receivedDataList = new ArrayList<>();
    final int FlyDatasize = 16;
    final int AskCount = 12;
    private DatabaseHelper databaseHelper;
    private byte select_charging_src = 0x1;
    /** For command 0081 */

    /**
     * for polling update
     */
    private List<Byte> deviceList;
    private int currentIndex;
    private boolean isPolling;
    private Handler handler = new Handler(Looper.getMainLooper());
    private CountDownTimer countDownTimer;
    private static final long TIMEOUT_DURATION_MS = 3500; // 3秒超时
    private static String TARGET_CMD;
    /**
     * for polling update
     */
    private boolean isSetRTCOnly = false;
    private ProgressCallback progressCallback;

    /**
     * for fw update
     */
    public static String filepath;
    public static List<String> updateHexStrings = new ArrayList<>();
    public static List<UpdateSubData> updateSubDatas = new ArrayList<>();
    private boolean isFwUpdating = false;
    private boolean isInBootloader = false;
    private boolean isChcekingDataCorrect = false;
    int fw_upgrade_count = 0;

    BluetoothGattCharacteristic characteristic;
    BluetoothGattDescriptor descriptor;

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        Log.w(TAG, "onStartCommand");
        if (!eventBus.isRegistered(this))
            eventBus.register(this);

        // init DB
        databaseHelper = new DatabaseHelper(this, FlightData.DB_NAME_FLIGHT_PLANNING);
        databaseHelper.getWritableDatabase();
        return super.onStartCommand(intent, flags, startId);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        disconnect();
        eventBus.unregister(this);
        Log.w(TAG, "onDestroy");
    }

    private final BluetoothGattCallback mGattCallback = new BluetoothGattCallback() {
        @Override
        public void onConnectionStateChange(BluetoothGatt gatt, int status, int newState) {
            String intentAction;
            if (newState == BluetoothProfile.STATE_CONNECTED) {
                //intentAction = ACTION_GATT_CONNECTED;
                mConnectionState = STATE_CONNECTED;
                //broadcastUpdate(intentAction);
                Log.i(TAG, "Connected to GATT server.");
                // Attempts to discover services after successful connection.
                Log.i(TAG, "Attempting to start service discovery:" +
                        mBluetoothGatt.discoverServices());

                String deviceName = gatt.getDevice().getName();
                Log.d(TAG, gatt.getDevice().getName() + ": connetced");
                Log.e(TAG, "MAC connetced: " + gatt.getDevice().getAddress());
                // 呼叫註冊的監聽器方法
                if (mConnectionListener != null) {
                    mConnectionListener.onBluetoothConnected(gatt);
                }
                showToast(LeDataService.this, deviceName + " : " + getResources().getString(R.string.comm_connected));

            } else if (newState == BluetoothProfile.STATE_DISCONNECTED) {
                //intentAction = ACTION_GATT_DISCONNECTED;
                mConnectionState = STATE_DISCONNECTED;
                Log.i(TAG, "Disconnected from GATT server.");
                // 呼叫註冊的監聽器方法
                if (mConnectionListener != null) {
                    mConnectionListener.onBluetoothDisconnected(gatt);
                }
                mBluetoothDeviceAddress = null;
                showToast(LeDataService.this, getResources().getString(R.string.comm_connect_failure));
                //broadcastUpdate(intentAction);
            }
        }

        @Override
        public void onServicesDiscovered(BluetoothGatt gatt, int status) {

//            BluetoothGattCharacteristic characteristic;
//            BluetoothGattDescriptor descriptor;

            if (status == BluetoothGatt.GATT_SUCCESS) {
                //broadcastUpdate(ACTION_GATT_SERVICES_DISCOVERED);

                Log.e(TAG, "gatt.getDevice().getName() : " + gatt.getDevice().getName());

                // SGN-168
                if (gatt.getDevice().getName().equals(Config.PIGEON_CHARGING_BOARD)) {
                    //gatt.requestMtu(64); // set MTU
                    characteristic = gatt.getService(GATT_SERVICE).getCharacteristic(READ_CHARACTERISTIC);
                    for (int k = 0; k < characteristic.getDescriptors().size(); k++) {
                        descriptor = (BluetoothGattDescriptor) characteristic.getDescriptors().get(k);
                        descriptor.setValue(BluetoothGattDescriptor.ENABLE_NOTIFICATION_VALUE);
                        gatt.writeDescriptor(descriptor);
                    }
                    gatt.setCharacteristicNotification(characteristic, true);
                }
            } else {
                Log.w(TAG, "1 onServicesDiscovered received: " + status);
            }
        }

        @Override
        public void onCharacteristicRead(BluetoothGatt gatt,
                                         BluetoothGattCharacteristic characteristic,
                                         int status) {
            if (status == BluetoothGatt.GATT_SUCCESS) {
                //broadcastUpdate(ACTION_DATA_AVAILABLE, characteristic);
                Log.d(TAG, "onCharacteristic Read");
            } else {
                Log.d(TAG, "Characteristic Read failed, status: " + status);
            }
        }

        @Override
        public void onCharacteristicWrite(BluetoothGatt gatt, BluetoothGattCharacteristic characteristic, int status) {
            if (status == BluetoothGatt.GATT_SUCCESS) {
                // 寫入成功
                int dataLength = characteristic.getValue().length;
//                Log.d(TAG, "Data sent: " + dataLength + " bytes");
//                Log.d(TAG, "Characteristic properties: " + characteristic.getProperties());
//                Log.d(TAG, "Characteristic write successful");
            } else {
                // 寫入失敗
                Log.d(TAG, "Characteristic write failed, status: " + status);
            }
        }

        @Override
        public void onMtuChanged(BluetoothGatt gatt, int mtu, int status) {
            super.onMtuChanged(gatt, mtu, status);
            //enableIndication(gatt, characteristic); // after mtu changed then writeDescriptor to avoid stuck, maybe Android issue.
            Log.d(TAG, "MTU Changed: " + mtu + " / status : " + status);
        }

        @Override
        public void onCharacteristicChanged(BluetoothGatt gatt,
                                            BluetoothGattCharacteristic characteristic) {
            //broadcastUpdate(ACTION_DATA_AVAILABLE, characteristic);
            if (gatt.getDevice().getName().equals(Config.PIGEON_CHARGING_BOARD)) {
                byte[] values = characteristic.getValue();

                Log.w(TAG, "Res : " + bytesToHex(values, values.length));
//                if(byteToHex(values[2]).equals("FF")) {
//                    Log.d(TAG, "ring response timeout!!!");
//                }
//                Log.e(TAG, "----------------------------");

                // others opcode
                if (values.length > 7 && byteToHex(values[0]).equals("FF")) {
                    CHECK_OPCODE = parseOpCode(values[6], values[7]);
                } else {
                    // invalid or no op code
                    CHECK_OPCODE = "FFFF";
                }

                if (byteToHex(values[0]).equals("FF") && CHECK_OPCODE.equals("8081") && values.length == 12) {
                    CHECK_OPCODE = parseOpCode(values[6], values[7]);
                    TARGET_MSG_LENGTH = hexStringToInt(byteToHex(values[1]));
                    TOTAL_FLIGHT_DATA_LEN = Command0081.GetResponceHowmanyDataSaved(values);
                    totalExpectedLength = TOTAL_FLIGHT_DATA_LEN; // 全部資料長度
                    if (TOTAL_FLIGHT_DATA_LEN == 0) {
                        if(progressCallback != null) {
                            progressCallback.onProgressFinished();
                        }
                        showToast(LeDataService.this, getResources().getString(R.string.settings_flight_data_none));
                        return;
                    }

                    if (!IS_START_REQUEST_FLY_DATA) {
                        if (isPolling)
                            select_charging_src = (byte) (currentIndex + 1);
                        Command0081 command0081 = new Command0081((byte) 0x3, select_charging_src);
                        command0081.setRequestDataOffect(0);
                        DATAOFFECT = command0081.getRequestDataOffect();
                        command0081.setRequestDatalength((byte) (FlyDatasize * AskCount));
                        command0081.RequestResponceFlyData(true);
                        sendMsg(command0081.GetSendData());
                        IS_START_REQUEST_FLY_DATA = true;
                    }
                }

                if (values.length > 12 && byteToHex(values[0]).equals("FF")) {
                    SIGNAL_EXPECTED_DATA_LENGTH = hexStringToInt(byteToHex(values[1])) + 2; // added sync & checksum two byte
                }
                /** CMD 0081 */
                if (isCmd0081 && IS_START_REQUEST_FLY_DATA) {
                    if (values.length != 12) {
                        cancelTimeoutTImer();
                        totalReceivedLength += values.length;
                        receivedData.put(values);
                    }
                    if (receivedData.position() == SIGNAL_EXPECTED_DATA_LENGTH) {
                        totalReceivedLength = totalReceivedLength - 13; // remove protocol and checksum
                        // 資料已接收完畢
                        byte[] receivedBytes = new byte[SIGNAL_EXPECTED_DATA_LENGTH];
                        receivedData.flip(); // 準備讀取數據
                        receivedData.get(receivedBytes); // 將 ByteBuffer 中的數據複製到 byte 陣列中
                        // orig code
                        processReceivedData(receivedBytes);
                        Log.w(TAG, "Total Received Length: " + totalReceivedLength + " / " + totalExpectedLength);
                        // 重置 ByteBuffer
                        receivedData.clear();
                    }
                    /** CMD 0012 */
                } else if (!isCmd0081 && CHECK_OPCODE.equals("8012") && byteToHex(values[0]).equals("FF")) {
                    // Continue to poll next device if polling
                    Log.d(TAG, "Get 8012 ACK!!!!!");
                    if (isPolling) {
                        showToast(LeDataService.this, (currentIndex + 1) + " : " + getResources().getString(R.string.settings_rtc_set_ok));
                        cancelTimeoutTImer();
                        if (isSetRTCOnly)
                            currentIndex++;
                        if (currentIndex < deviceList.size()) {
                            if (isSetRTCOnly)
                                TARGET_CMD = CMD_0012;
                            else
                                TARGET_CMD = CMD_0011;
                            handler.postDelayed(pollNextDeviceRunnable, TIMEOUT_DURATION_MS); // 在收到ACK后延迟3秒再执行
                        } else {
                            Log.w(TAG, "Polling completed for all devices");
                            if (TARGET_CMD.equals(CMD_0081))
                                progressCallback.onProgressFinished();
                            isPolling = false;
                        }
                    } else {
                        Log.e(TAG, "year raw : " + byteToHex(values[8]));
                        showToast(LeDataService.this, (select_charging_src) + " : " + getResources().getString(R.string.settings_rtc_set_ok) + ", " + parseYear(byteToHex(values[8])));
                        if (isSetRTCOnly)
                            processSetFlightConfig(false);
                        else
                            processSetFlightConfig(true);
                    }
                    /** CMD 0011 */
                } else if (!isCmd0081 && CHECK_OPCODE.equals("8011") && byteToHex(values[0]).equals("FF")) {
                    Log.d(TAG, "Get 8011 ACK!!!!!");
                    if (isPolling) {
                        showToast(LeDataService.this, (currentIndex + 1) + " : " + getResources().getString(R.string.settings_flight_config_set_ok));
                        cancelTimeoutTImer();
                        currentIndex++;
                        if (currentIndex < deviceList.size()) {
                            TARGET_CMD = CMD_0012;
                            handler.postDelayed(pollNextDeviceRunnable, TIMEOUT_DURATION_MS); // 在收到ACK后延迟3秒再执行
                        } else {
                            Log.w(TAG, "Polling completed for all devices");
                            isPolling = false;
                        }
                    } else {
                        cancelTimeoutTImer();
                        showToast(LeDataService.this, (select_charging_src) + " : " + getResources().getString(R.string.settings_flight_config_set_ok));
                    }
                    /** Query FW VERSION 0001 */
                } else if (!isCmd0081 && CHECK_OPCODE.equals("8001") && byteToHex(values[0]).equals("FF")) {
                    if (isPolling) {
                        cancelTimeoutTImer();
                        currentIndex++;
                        if (currentIndex < deviceList.size()) {
                            TARGET_CMD = CMD_0001;
                            sendEventMsg(CMD_0001, GetFirmwareVersionToStr(values), (byte) (currentIndex));
                            handler.postDelayed(pollNextDeviceRunnable, TIMEOUT_DURATION_MS); // 在收到ACK后延迟3秒再执行
                        } else {
                            sendEventMsg(CMD_0001, GetFirmwareVersionToStr(values), (byte) (currentIndex));
                            Log.w(TAG, "Polling completed for all devices");
                            isPolling = false;
                        }
                    } else {
                        cancelTimeoutTImer();
                        sendEventMsg(CMD_0001, GetFirmwareVersionToStr(values), select_charging_src);
                    }
                    /** FW UPGRADE 0002 */
                } else if (!isCmd0081 && CHECK_OPCODE.equals("8002") && byteToHex(values[0]).equals("FF")) {
//                    Log.d(TAG, "Get 8002 ACK!!!!!");
//                    Log.w(TAG, "Res : " + bytesToHex(values, values.length));
                    if(byteToHex(values[8]).equals("03")) { // memory write
                        handler.postDelayed(new Runnable() {
                            @Override
                            public void run() {
                                cmd0002_mem_read();
                            }
                        }, 120);
//                        cmd0002_mem_read();
                    } else if(byteToHex(values[8]).equals("02")) { // memory read
                        verifyWrittenData(values);
                    }
                    /** GET LFID, CMD_013 */
                } else if (!isCmd0081 && CHECK_OPCODE.equals("8013") && byteToHex(values[0]).equals("FF")) {
                    Log.d(TAG, "Get 8013 ACK!!!!!");
                    if (isPolling) {
//                        showToast(LeDataService.this, (currentIndex + 1) + " : Set flight Config OK!");
                        cancelTimeoutTImer();
                        currentIndex++;
                        if (currentIndex < deviceList.size()) {
                            TARGET_CMD = CMD_0013;
                            sendEventMsg(CMD_0013, parseLfid(bytesToHex(values, values.length)), (byte) (currentIndex));
                            handler.postDelayed(pollNextDeviceRunnable, TIMEOUT_DURATION_MS); // 在收到ACK后延迟3秒再执行
                        } else {
                            sendEventMsg(CMD_0013, parseLfid(bytesToHex(values, values.length)), (byte) (currentIndex));
                            Log.w(TAG, "Polling completed for all devices");
                            isPolling = false;
                        }
                    } else {
                        cancelTimeoutTImer();
                        sendEventMsg(CMD_0013, parseLfid(bytesToHex(values, values.length)), select_charging_src);
                    }
                }
            }
        }
    };

    public class LocalBinder extends Binder {
        public LeDataService getService() {
            return LeDataService.this;
        }
    }

    @Override
    public IBinder onBind(Intent intent) {
        return mBinder;
    }

    @Override
    public boolean onUnbind(Intent intent) {
        // After using a given device, you should make sure that BluetoothGatt.close() is called
        // such that resources are cleaned up properly.  In this particular example, close() is
        // invoked when the UI is disconnected from the Service.
        Log.w(TAG, "onUnbind");
        close();
        return super.onUnbind(intent);
    }

    private final IBinder mBinder = new LocalBinder();


    /**
     * Initializes a reference to the local Bluetooth adapter.
     *
     * @return Return true if the initialization is successful.
     */
    public boolean initialize() {
        // For API level 18 and above, get a reference to BluetoothAdapter through
        // BluetoothManager.
        if (mBluetoothManager == null) {
            mBluetoothManager = (BluetoothManager) getSystemService(Context.BLUETOOTH_SERVICE);
            if (mBluetoothManager == null) {
                Log.e(TAG, "Unable to initialize BluetoothManager.");
                return false;
            }
        }

        mBluetoothAdapter = mBluetoothManager.getAdapter();
        if (mBluetoothAdapter == null) {
            Log.e(TAG, "Unable to obtain a BluetoothAdapter.");
            return false;
        }

        return true;
    }


    /**
     * Connects to the GATT server hosted on the Bluetooth LE device.
     *
     * @param address The device address of the destination device.
     * @return Return true if the connection is initiated successfully. The connection result
     * is reported asynchronously through the
     * {@code BluetoothGattCallback#onConnectionStateChange(android.bluetooth.BluetoothGatt, int, int)}
     * callback.
     */
    public boolean connect(final String address) {
        if (mBluetoothAdapter == null || address == null) {
            Log.w(TAG, "BluetoothAdapter not initialized or unspecified address.");
            return false;
        }

        // Previously connected device.  Try to reconnect.
        if (mBluetoothDeviceAddress != null && address.equals(mBluetoothDeviceAddress)
                && mBluetoothGatt != null) {
            Log.d(TAG, "mBluetoothDeviceAddress : " + mBluetoothDeviceAddress);
            Log.d(TAG, "Trying to use an existing mBluetoothGatt for connection.");
            if(mConnectionListener != null) {
                mConnectionListener.onBluetoothAlreadyConnected();
            }
            return true;
        }

        final BluetoothDevice device = mBluetoothAdapter.getRemoteDevice(address);
        if (device == null) {
            Log.w(TAG, "Device not found.  Unable to connect.");
            return false;
        }
        // We want to directly connect to the device, so we are setting the autoConnect
        // parameter to false.
        mBluetoothGatt = device.connectGatt(this, false, mGattCallback);
        Log.d(TAG, "Trying to create a new connection.");
        mBluetoothDeviceAddress = address;
        mConnectionState = STATE_CONNECTING;
        return true;
    }

    /**
     * Disconnects an existing connection or cancel a pending connection. The disconnection result
     * is reported asynchronously through the
     * {@code BluetoothGattCallback#onConnectionStateChange(android.bluetooth.BluetoothGatt, int, int)}
     * callback.
     */
    public void disconnect() {
//        if (mBluetoothAdapter == null || mBluetoothGatt == null) {
        if (mBluetoothGatt == null) {
            Log.w(TAG, "BluetoothAdapter not initialized");
            return;
        }
        mBluetoothGatt.disconnect();
    }

    /**
     * After using a given BLE device, the app must call this method to ensure resources are
     * released properly.
     */
    public void close() {
        mConnectionState = STATE_DISCONNECTED;
        if (mBluetoothGatt == null) {
            return;
        }
        mBluetoothGatt.close();
        mBluetoothGatt = null;
    }

    /**
     * Request a read on a given {@code BluetoothGattCharacteristic}. The read result is reported
     * asynchronously through the {@code BluetoothGattCallback#onCharacteristicRead(android.bluetooth.BluetoothGatt, android.bluetooth.BluetoothGattCharacteristic, int)}
     * callback.
     *
     * @param characteristic The characteristic to read from.
     */
    public void readCharacteristic(BluetoothGattCharacteristic characteristic) {
        if (mBluetoothAdapter == null || mBluetoothGatt == null) {
            Log.w(TAG, "BluetoothAdapter not initialized");
            return;
        }
        mBluetoothGatt.readCharacteristic(characteristic);
    }

    /**
     * Enables or disables notification on a give characteristic.
     *
     * @param characteristic Characteristic to act on.
     * @param enabled If true, enable notification.  False otherwise.
     */
    /*
    public void setCharacteristicNotification(BluetoothGattCharacteristic characteristic,
                                              boolean enabled) {
        if (mBluetoothAdapter == null || mBluetoothGatt == null) {
            Log.w(TAG, "BluetoothAdapter not initialized");
            return;
        }
        mBluetoothGatt.setCharacteristicNotification(characteristic, enabled);

        // This is specific to Heart Rate Measurement.
        if (GattAttributes.equals(characteristic.getUuid())) {
            BluetoothGattDescriptor descriptor = characteristic.getDescriptor(
                    UUID.fromString(GattAttributes.CLIENT_CHARACTERISTIC_CONFIG));
            descriptor.setValue(BluetoothGattDescriptor.ENABLE_NOTIFICATION_VALUE);
            mBluetoothGatt.writeDescriptor(descriptor);
        }
    }
    */

    /**
     * Retrieves a list of supported GATT services on the connected device. This should be
     * invoked only after {@code BluetoothGatt#discoverServices()} completes successfully.
     *
     * @return A {@code List} of supported services.
     */
    public List<BluetoothGattService> getSupportedGattServices() {
        if (mBluetoothGatt == null) return null;

        return mBluetoothGatt.getServices();
    }

    private void displayGattServices(BluetoothGatt gatt) {
        if (gatt == null) {
            return;
        }
        List<BluetoothGattService> gattServices = gatt.getServices();
        if (gattServices == null) {
            return;
        }
        String uuid = null;
        for (BluetoothGattService gattService : gattServices) {
            uuid = gattService.getUuid().toString();
            List<BluetoothGattCharacteristic> gattCharacteristics =
                    gattService.getCharacteristics();
            for (BluetoothGattCharacteristic gattCharacteristic : gattCharacteristics) {
                uuid = gattCharacteristic.getUuid().toString();
                int prop = gattCharacteristic.getProperties();
                enableIndication(gatt, gattCharacteristic);
                Log.w(TAG, " -" + uuid + " : " + ", Prop = " + prop);
            }
        }
    }

    private void enableIndication(BluetoothGatt gatt, BluetoothGattCharacteristic characteristic) {
        Log.d(TAG, "enableIndication " + gatt.getDevice().getName() + " - " + gatt.getDevice().getAddress());
        for (BluetoothGattDescriptor descriptor : characteristic.getDescriptors()) {
            descriptor.setValue(BluetoothGattDescriptor.ENABLE_INDICATION_VALUE);
            gatt.writeDescriptor(descriptor);
        }
        gatt.setCharacteristicNotification(characteristic, true);

        /*
        characteristic = gatt.getService(GATT_SERVICE).getCharacteristic(READ_CHARACTERISTIC);
        for (int k = 0; k < characteristic.getDescriptors().size(); k++) {
            descriptor = (BluetoothGattDescriptor) characteristic.getDescriptors().get(k);
            descriptor.setValue(BluetoothGattDescriptor.ENABLE_NOTIFICATION_VALUE);
            gatt.writeDescriptor(descriptor);
        }
        gatt.setCharacteristicNotification(characteristic, true);
        */
    }

    private boolean sendMsg(byte[] writeByte) {
        try {
            BluetoothGattCharacteristic characteristic = mBluetoothGatt.getService(GATT_SERVICE).getCharacteristic(WRITE_CHARACTERISTIC);
            characteristic.setValue(writeByte);
            mBluetoothGatt.writeCharacteristic(characteristic);
//            Log.e(TAG, " ---------------------------- ");

            /*
            String memWrite = byteToHex(writeByte[8]);
            if(memWrite.equals("03")) {
                byte[] sendAddress = Arrays.copyOfRange(writeByte, 9, writeByte.length - 1);
                String sendPayloadHex = bytesToHex(sendAddress, sendAddress.length);
                Log.w(TAG, "send payload : " + sendPayloadHex);
            }
            */
            Log.w(TAG, " send hex : " + bytesToHex(writeByte, writeByte.length));
//            Log.e(TAG, " ---------------------------- ");
            return true;
        } catch (NullPointerException e) {
            Log.e(TAG, "NullPointerException : " + e);
            showToast(LeDataService.this, getResources().getString(R.string.comm_check_connection_state));
            return false;
        } catch (Exception e) {
            Log.e(TAG, "Exception : " + e);
            return false;
        }
    }

    @Subscribe
    private void sendEventMsg(String Cmd, String message, byte charging_src) {
        // 發送事件到EventBus
        GeneralMsgEvent generalMsgEvent = new GeneralMsgEvent(Cmd, message, charging_src);
        eventBus.post(generalMsgEvent);
    }

    // EventBus
    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onMessageEvent(MessageEvent event) {
        String message = event.getMessage();
        select_charging_src = event.getCharging_src();
        // clear ByteBuffer
        if (receivedData != null && receivedData.remaining() > 0) {
            receivedData.clear();
            receivedData = ByteBuffer.allocate(1024);
        }
        TARGET_CMD = message;
        if (message.equals(CMD_0081)) {
            IS_START_REQUEST_FLY_DATA = false;
            isCmd0081 = true;
            totalReceivedLength = 0;
            // clear byte list
            receivedDataList.clear();

            if (select_charging_src == (byte) 0x5) {
                startPolling();
                progressCallback.getCurrentDevice(currentIndex + 1);
                progressCallback.showNextDialog();
            } else {
                progressCallback.showNextDialog();
                byte[] writeByte = CommandExecutor.execute(message, select_charging_src);
                sendMsg(writeByte);
                startTimeoutTimer(false);
            }
        } else if (message.equals(CMD_0013)) {
            if (select_charging_src == (byte) 0x5) {
                startPolling();
            } else {
                byte[] writeByte = CommandExecutor.execute(message, select_charging_src);
                sendMsg(writeByte);
                startTimeoutTimer(false);
            }
        } else if (message.equals(CMD_0081_DEL_DATA)) {
            if (select_charging_src == (byte) 0x5) {
                startPolling();
            } else {
                byte[] writeByte = CommandExecutor.execute(message, select_charging_src);
                sendMsg(writeByte);
                startTimeoutTimer(false);
            }
            /** Query FW VERSION */
        } else if (message.equals(CMD_QUERY_FW_VER)) {
            if (select_charging_src == (byte) 0x5) {
                startPolling();
            } else {
                byte[] writeByte = CommandExecutor.execute(message, select_charging_src);
                sendMsg(writeByte);
            }
            /** Query Charging boardFW VERSION */
        } else if (message.equals(CMD_00FA)) {
            byte[] writeByte = CommandExecutor.execute(message, select_charging_src);
            sendMsg(writeByte);
            /** Switch to bootloader */
        } else if (message.equals(CMD_SWITCH_BOOTLOADER)) {
            if (select_charging_src == (byte) 0x5) {
                startPolling();
            } else {
                byte[] writeByte = CommandExecutor.execute(message, select_charging_src);
                sendMsg(writeByte);
            }
            /** Switch to app mode */
        } else if (message.equals(CMD_SWITCH_APP_MODE)) {
            if (select_charging_src == (byte) 0x5) {
                startPolling();
            } else {
                byte[] writeByte = CommandExecutor.execute(message, select_charging_src);
                sendMsg(writeByte);
            }
            /** Get OP code */
        } else if (message.equals(CMD_GET_OP_MODE)) {
            if (select_charging_src == (byte) 0x5) {
                startPolling();
            } else {
                byte[] writeByte = CommandExecutor.execute(message, select_charging_src);
                sendMsg(writeByte);
            }
            /** Get UP GRADE FW */
        } else if (message.equals(CMD_UPGRADE_FW)) {
            new AFPUpdateTask().executeCheckHexFile(DOWNLOAD_PATH+HEX_FILE_NAME);
            try {
                sleep(500);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            /*
            byte[] writeByte1 = CommandExecutor.execute(CMD_SWITCH_BOOTLOADER, select_charging_src);
            sendMsg(writeByte1);
            Log.w(TAG, "switch to bootloader!!!!!");
            try {
                sleep(6000);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            */
//            byte[] writeByte = CommandExecutor.execute(CMD_GET_OP_MODE, select_charging_src);
//            sendMsg(writeByte);
            upgradeProcessTesting();
            try {
                sleep(2000);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
//            Log.w(TAG, "switch to app mode!!!!!");
//            byte[] writeByte = CommandExecutor.execute(CMD_SWITCH_APP_MODE, select_charging_src);
//            sendMsg(writeByte);
        } else {
            isCmd0081 = false;
            // here for pooling.
            if (select_charging_src == (byte) 0x5) {
                isPolling = true;
                if (message.equals(CMD_0012)) {
                    isSetRTCOnly = true;
                } else if (message.equals(CMD_0011)) {
                    isSetRTCOnly = false;
                    TARGET_CMD = CMD_0012;
                }
                startPolling();
                // here for signal.
            } else {
                if (message.equals(CMD_0011)) { // set flight config, 1, CMD_0012 then 2, CMD_0011
                    byte[] writeByte = CommandExecutor.execute(CMD_0012, select_charging_src);
                    sendMsg(writeByte);
                    startTimeoutTimer(false);
                } else if (message.equals(CMD_0012)) {
                    isSetRTCOnly = true;
                    byte[] writeByte = CommandExecutor.execute(message, select_charging_src);
                    sendMsg(writeByte);
                    startTimeoutTimer(false);
                }
            }
        }
    }

    // max ring number
    private void initDeviceList() {
        deviceList = new ArrayList<>();
        for (byte i = 0x1; i <= 0x4; i++) {
            deviceList.add(i);
        }
    }

    private void startPolling() {
        if (deviceList == null) {
            initDeviceList();
        }
        currentIndex = 0;
        isPolling = true;
        pollNextDevice();
    }

    private Runnable pollNextDeviceRunnable = new Runnable() {
        @Override
        public void run() {
            if (currentIndex < deviceList.size()) {
                byte device = deviceList.get(currentIndex);
                if (TARGET_CMD.equals(CMD_0081)) {
                    initGetFlightData();
                } else if (TARGET_CMD.equals(CMD_0081_DEL_DATA)) {
                    byte[] writeByte = CommandExecutor.execute(TARGET_CMD, device);
                    sendMsg(writeByte);
                    startTimeoutTimer(true);
                } else {
                    byte[] writeByte = CommandExecutor.execute(TARGET_CMD, device);
                    Log.w(TAG, "Polling : " + TARGET_CMD + " / device : " + device);
                    sendMsg(writeByte);
                    startTimeoutTimer(true);
                }
            } else {
                if (TARGET_CMD.equals(CMD_0081)) {
                    if(progressCallback != null) {
                        progressCallback.onProgressFinished();
                    }
                }
                Log.w(TAG, "Polling completed for all devices");
                isPolling = false;
                isSetRTCOnly = false;
                currentIndex = 0;
            }
        }
    };

    private void startTimeoutTimer(boolean isPolling) {
        cancelTimeoutTImer();
        countDownTimer = new CountDownTimer(TIMEOUT_DURATION_MS, TIMEOUT_DURATION_MS) {
            public void onTick(long millisUntilFinished) {
                // 每秒钟计时器都会调用此方法，但我们不需要处理此事件
            }

            public void onFinish() {
                // 超时后执行
                Log.e(TAG, "Timeout! Proceeding to next device : " + TARGET_CMD + " isPolling : " + isPolling);
                if (isPolling) {
                    showToast(LeDataService.this, (currentIndex + 1) + " : " + getResources().getString(R.string.settings_flight_config_set_fail_polling));
                    // 超时后增加 currentIndex 并发送下一个设备的命令
                    currentIndex++;
                    if (TARGET_CMD.equals(CMD_0081)) {
                        progressCallback.onProgressFinished();
                        progressCallback.getCurrentDevice(currentIndex + 1);
                        progressCallback.showNextDialog();
                    }
                    pollNextDevice();
                } else {
                    showToast(LeDataService.this, (select_charging_src) + " : " + getResources().getString(R.string.settings_flight_config_set_fail_signal));
//                    Log.w(TAG, "TARGET_CMD : " + TARGET_CMD);
                    if (TARGET_CMD.equals(CMD_0081)) {
                        if(progressCallback != null) {
                            progressCallback.onProgressFinished(); // finished callback
                            isSetRTCOnly = false;
                        }
                    }
                    isCmd0081 = false;
                }
            }
        }.start();
    }

    private void cancelTimeoutTImer() {
        if (countDownTimer != null) {
            countDownTimer.cancel();
        }
    }

    private void pollNextDevice() {
        handler.postDelayed(pollNextDeviceRunnable, 0);
    }

    public int getGattStatus() {
        return mConnectionState;
    }

    public void registerBluetoothConnectionListener(BluetoothConnectionListener listener) {
        mConnectionListener = listener;
    }

    public void unregisterBluetoothConnectionListener() {
        mConnectionListener = null;
    }

    public interface BluetoothConnectionListener {
        void onBluetoothConnected(BluetoothGatt gatt);
        void onBluetoothDisconnected(BluetoothGatt gatt);
        void onBluetoothConnecting(BluetoothGatt gatt);
        void onBluetoothAlreadyConnected();
    }

    private void processReceivedData(byte[] data) {

        // 计算进度
        int progress = 0;
        if (totalExpectedLength > 0) {
            progress = (int) ((totalReceivedLength / (float) totalExpectedLength) * 100);
        }

        // 调用回调
        if (progressCallback != null) {
            progressCallback.onProgressUpdate(progress); // progress callback
        }

        receivedDataList.add(data.clone());
        Log.d(TAG, "Received data (hex): " + bytesToHex(data, data.length));
        // save to database
        if (!isPayloadExist(data)) {
            savingToDatabase(data);
        } else {
            Log.w(TAG, "payload data exist!!!!");
        }
        Command0081 command0081 = new Command0081((byte) 0x3, select_charging_src);
        DATAOFFECT = DATAOFFECT + (FlyDatasize * AskCount);
        command0081.setRequestDataOffect(DATAOFFECT);
        int remainingLength = totalExpectedLength - totalReceivedLength;
        int requestLength = Math.min(remainingLength, FlyDatasize * AskCount); // update request length

        if (totalReceivedLength >= totalExpectedLength) {
            totalExpectedLength = 0;
            progressCallback.onProgressFinished(); // finished callback
            showToast(LeDataService.this, (select_charging_src) + " : " + getResources().getString(R.string.settings_flight_data_rece_success));
            IS_START_REQUEST_FLY_DATA = false;
            if (isPolling) {
                currentIndex++;
                progressCallback.getCurrentDevice(currentIndex + 1);
                handler.postDelayed(pollNextDeviceRunnable, TIMEOUT_DURATION_MS);
                if (currentIndex < 4) {
                    progressCallback.showNextDialog();
                }
            }
            Log.e(TAG, " received success!");
            isCmd0081 = false;
        } else {
            try {
                sleep(250);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            command0081.setRequestDatalength((byte) (requestLength));
            command0081.RequestResponceFlyData(true);
            sendMsg(command0081.GetSendData());
        }
    }

    private void processSetFlightConfig(boolean isUpdateFlightConfig) {
        if (isUpdateFlightConfig) {
            byte[] writeByte = CommandExecutor.execute(CMD_0011, select_charging_src);
            sendMsg(writeByte);
        } else {
            cancelTimeoutTImer();
//            Log.d(TAG, "update RTC only");
            isSetRTCOnly = false;
        }
    }

    private void savingToDatabase(byte[] data) {
        new query().saveFlightData(this, byteToHex(data[0]), byteToHex(data[1]), byteToHex(data[2]), byteToHex(data[3]),
                byteToHex(data[4]), byteToHex(data[5]), parseOpCode(data[6], data[7]), parsePayload(bytesToHex(data, data.length)),
                parseChecksum(bytesToHex(data, data.length)), parseRingCode(parsePayload(bytesToHex(data, data.length))), getDate());
    }

    private boolean isPayloadExist(byte[] data) {
        return new query().isFlightDataExist(this, parseRingCode(parsePayload(bytesToHex(data, data.length))),
                getDate(), parsePayload(bytesToHex(data, data.length)));
    }

    private void initGetFlightData() {
        // clear ByteBuffer
        if (receivedData != null && receivedData.remaining() > 0) {
            receivedData.clear();
            receivedData = ByteBuffer.allocate(1024);
        }
        IS_START_REQUEST_FLY_DATA = false;
        isCmd0081 = true;
        totalReceivedLength = 0;
        // clear byte list
        receivedDataList.clear();
        Log.e(TAG, "0081 index : " + currentIndex);
        if (currentIndex <= 3) {
            byte[] writeByte = CommandExecutor.execute(TARGET_CMD, (byte) (currentIndex + 1));
            sendMsg(writeByte);
            startTimeoutTimer(true);
        }
    }

    public interface ProgressCallback {
        void onProgressUpdate(int progress);
        void onProgressFinished();
        void showNextDialog();
        void getCurrentDevice(int deviceIndex);
    }

    public void setProgressCallback(ProgressCallback progressCallback) {
        this.progressCallback = progressCallback;
    }

    private void cmd0002_mem_read() {
        Command0002 command0002 = new Command0002((byte) 0x3, select_charging_src);
        command0002.SetMemControl(Command0002.MemControlType.READ);
        command0002.SetStartAddress(updateSubDatas.get(fw_upgrade_count).getAddressEffect());
        command0002.SetDataLength((byte) updateSubDatas.get(fw_upgrade_count).getBinDatas().size());
        byte[] send;
        send = command0002.GetSendData();
        sendMsg(send);
    }

    private void cmd0002_mem_write() {
        Command0002 command0002 = new Command0002((byte) 0x3, select_charging_src);
        command0002.SetMemControl(Command0002.MemControlType.WRITE);
        command0002.SetStartAddress(updateSubDatas.get(fw_upgrade_count).getAddressEffect());
        command0002.SetDataLength((byte) updateSubDatas.get(fw_upgrade_count).getBinDatas().size());
        byte[] send;
        send = command0002.GetSendData();
        sendMsg(send);
    }

    private boolean verifyWrittenData(byte[] responseValues) {
        if (fw_upgrade_count < updateSubDatas.size()) {
//        if (fw_upgrade_count < 3) {
            UpdateSubData currentData = updateSubDatas.get(fw_upgrade_count);
            byte[] receivedAddress = Arrays.copyOfRange(responseValues, 9, responseValues.length - 1);
            String receivedPayloadHex = bytesToHex(receivedAddress, receivedAddress.length);
            Log.w(TAG, "recv payload : " + receivedPayloadHex);
            // 比對邏輯：檢查收到的資料是否與發送的資料一致
            if (!COMPARE_PAYLOAD_LIST.isEmpty()) {
                String expectedPayloadHex = COMPARE_PAYLOAD_LIST.get(0);
                if (expectedPayloadHex.equals(receivedPayloadHex)) {
                    Log.w(TAG, "verify fw_upgrade_count : " + fw_upgrade_count);
//                    Log.i(TAG, "Payload match successful!");
                    fw_upgrade_count++;
                    if(fw_upgrade_count < updateSubDatas.size()) {
                        handler.postDelayed(new Runnable() {
                            @Override
                            public void run() {
                                upgradeProcessTesting();
                            }
                        }, 120);

                    } else {
                        Log.i(TAG, "Flash completed! - 1 ");
                        isFwUpdating = false;
                        fw_upgrade_count = 0;
                    }
                    return true;
                } else {
                    Log.e(TAG, "Payload mismatch! Expected: " + expectedPayloadHex + ", Received: " + receivedPayloadHex);
                }
            } else {
                Log.e(TAG, "COMPARE_PAYLOAD_LIST is empty, unable to verify payload.");
            }
        } else {
            Log.i(TAG, "Flash completed! - 2 ");
            isFwUpdating = false;
            fw_upgrade_count = 0;
        }
        return false;
    }

    private void upgradeProcessTesting() {
        isFwUpdating = true;
        Command0001 command0001 = new Command0001((byte) 0x3, select_charging_src);
        Command0002 command0002 = new Command0002((byte) 0x3, select_charging_src);
        byte[] send;
//        byte[] recv;
        byte opmode = (byte) 0xFF;
        boolean sendResult;

        int tryCount = 0;
        long applicationLength = 0;
        int applicationChecksum = 0xFFFF;
        int updateStep = 0;
        int cmpFailCount = 0;

//        Log.e(TAG, "updateSubDatas.size() : " + updateSubDatas.size());
//        Log.e(TAG, "updateHexStrings.size() : " + updateHexStrings.size());

        command0002.SetMemControl(Command0002.MemControlType.WRITE);
        command0002.SetStartAddress(updateSubDatas.get(fw_upgrade_count).getAddressEffect());
//        Log.e(TAG, "current i : " + fw_upgrade_count);
        command0002.SetWriteData(listToByte(updateSubDatas.get(fw_upgrade_count).getBinDatas()));
        send = command0002.GetSendData();
        sendMsg(send);
        if(!COMPARE_PAYLOAD_LIST.isEmpty())
            COMPARE_PAYLOAD_LIST.clear();
        byte[] sendPayload = Arrays.copyOfRange(send, 9, (send.length - 1));
        COMPARE_PAYLOAD_LIST.add(bytesToHex(sendPayload, sendPayload.length));

        /*
        for (fw_upgrade_count = 0; fw_upgrade_count < 2; ) {
            command0002.SetMemControl(Command0002.MemControlType.WRITE);
            command0002.SetStartAddress(updateSubDatas.get(fw_upgrade_count).getAddressEffect());
            Log.e(TAG, "current i : " + fw_upgrade_count);
            command0002.SetWriteData(listToByte(updateSubDatas.get(fw_upgrade_count).getBinDatas()));
            send = command0002.GetSendData();
            sendMsg(send);
            try {
                Thread.sleep(400);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
//            fw_upgrade_count++;
        }
        */
    }
}
