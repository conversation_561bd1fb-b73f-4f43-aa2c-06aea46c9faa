1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.android.pigeonringfwupdatetool"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <!-- Bluetooth permissions -->
12    <uses-permission
12-->C:\Users\<USER>\AndroidStudioProjects\PigeonRingFwUpdateTool\app\src\main\AndroidManifest.xml:6:5-95
13        android:name="android.permission.BLUETOOTH"
13-->C:\Users\<USER>\AndroidStudioProjects\PigeonRingFwUpdateTool\app\src\main\AndroidManifest.xml:6:22-65
14        android:maxSdkVersion="30" />
14-->C:\Users\<USER>\AndroidStudioProjects\PigeonRingFwUpdateTool\app\src\main\AndroidManifest.xml:6:66-92
15    <uses-permission
15-->C:\Users\<USER>\AndroidStudioProjects\PigeonRingFwUpdateTool\app\src\main\AndroidManifest.xml:7:5-101
16        android:name="android.permission.BLUETOOTH_ADMIN"
16-->C:\Users\<USER>\AndroidStudioProjects\PigeonRingFwUpdateTool\app\src\main\AndroidManifest.xml:7:22-71
17        android:maxSdkVersion="30" />
17-->C:\Users\<USER>\AndroidStudioProjects\PigeonRingFwUpdateTool\app\src\main\AndroidManifest.xml:7:72-98
18
19    <!-- Needed only if your app makes the device discoverable to Bluetooth devices. -->
20    <uses-permission android:name="android.permission.BLUETOOTH_ADVERTISE" />
20-->C:\Users\<USER>\AndroidStudioProjects\PigeonRingFwUpdateTool\app\src\main\AndroidManifest.xml:10:5-78
20-->C:\Users\<USER>\AndroidStudioProjects\PigeonRingFwUpdateTool\app\src\main\AndroidManifest.xml:10:22-75
21    <!-- Needed only if your app communicates with already-paired Bluetooth devices. -->
22    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
22-->C:\Users\<USER>\AndroidStudioProjects\PigeonRingFwUpdateTool\app\src\main\AndroidManifest.xml:12:5-76
22-->C:\Users\<USER>\AndroidStudioProjects\PigeonRingFwUpdateTool\app\src\main\AndroidManifest.xml:12:22-73
23    <!-- Needed for Bluetooth scanning -->
24    <uses-permission
24-->C:\Users\<USER>\AndroidStudioProjects\PigeonRingFwUpdateTool\app\src\main\AndroidManifest.xml:14:5-120
25        android:name="android.permission.BLUETOOTH_SCAN"
25-->C:\Users\<USER>\AndroidStudioProjects\PigeonRingFwUpdateTool\app\src\main\AndroidManifest.xml:14:22-70
26        android:usesPermissionFlags="neverForLocation" />
26-->C:\Users\<USER>\AndroidStudioProjects\PigeonRingFwUpdateTool\app\src\main\AndroidManifest.xml:14:71-117
27
28    <!-- Needed only if your app uses Bluetooth scan results to derive physical location. -->
29    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
29-->C:\Users\<USER>\AndroidStudioProjects\PigeonRingFwUpdateTool\app\src\main\AndroidManifest.xml:17:5-79
29-->C:\Users\<USER>\AndroidStudioProjects\PigeonRingFwUpdateTool\app\src\main\AndroidManifest.xml:17:22-76
30    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
30-->C:\Users\<USER>\AndroidStudioProjects\PigeonRingFwUpdateTool\app\src\main\AndroidManifest.xml:18:5-81
30-->C:\Users\<USER>\AndroidStudioProjects\PigeonRingFwUpdateTool\app\src\main\AndroidManifest.xml:18:22-78
31
32    <!-- File access permissions -->
33    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
33-->C:\Users\<USER>\AndroidStudioProjects\PigeonRingFwUpdateTool\app\src\main\AndroidManifest.xml:21:5-80
33-->C:\Users\<USER>\AndroidStudioProjects\PigeonRingFwUpdateTool\app\src\main\AndroidManifest.xml:21:22-77
34    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
34-->C:\Users\<USER>\AndroidStudioProjects\PigeonRingFwUpdateTool\app\src\main\AndroidManifest.xml:22:5-81
34-->C:\Users\<USER>\AndroidStudioProjects\PigeonRingFwUpdateTool\app\src\main\AndroidManifest.xml:22:22-78
35    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" />
35-->C:\Users\<USER>\AndroidStudioProjects\PigeonRingFwUpdateTool\app\src\main\AndroidManifest.xml:23:5-82
35-->C:\Users\<USER>\AndroidStudioProjects\PigeonRingFwUpdateTool\app\src\main\AndroidManifest.xml:23:22-79
36
37    <!-- Bluetooth LE feature -->
38    <uses-feature
38-->C:\Users\<USER>\AndroidStudioProjects\PigeonRingFwUpdateTool\app\src\main\AndroidManifest.xml:26:5-28:35
39        android:name="android.hardware.bluetooth_le"
39-->C:\Users\<USER>\AndroidStudioProjects\PigeonRingFwUpdateTool\app\src\main\AndroidManifest.xml:27:9-53
40        android:required="true" />
40-->C:\Users\<USER>\AndroidStudioProjects\PigeonRingFwUpdateTool\app\src\main\AndroidManifest.xml:28:9-32
41
42    <permission
42-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf97017a4abeee10e28abc3dc8534f83\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
43        android:name="com.android.pigeonringfwupdatetool.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
43-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf97017a4abeee10e28abc3dc8534f83\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
44        android:protectionLevel="signature" />
44-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf97017a4abeee10e28abc3dc8534f83\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
45
46    <uses-permission android:name="com.android.pigeonringfwupdatetool.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
46-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf97017a4abeee10e28abc3dc8534f83\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
46-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf97017a4abeee10e28abc3dc8534f83\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
47
48    <application
48-->C:\Users\<USER>\AndroidStudioProjects\PigeonRingFwUpdateTool\app\src\main\AndroidManifest.xml:30:5-49:19
49        android:allowBackup="true"
49-->C:\Users\<USER>\AndroidStudioProjects\PigeonRingFwUpdateTool\app\src\main\AndroidManifest.xml:31:9-35
50        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
50-->[androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cf97017a4abeee10e28abc3dc8534f83\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
51        android:dataExtractionRules="@xml/data_extraction_rules"
51-->C:\Users\<USER>\AndroidStudioProjects\PigeonRingFwUpdateTool\app\src\main\AndroidManifest.xml:32:9-65
52        android:debuggable="true"
53        android:extractNativeLibs="false"
54        android:fullBackupContent="@xml/backup_rules"
54-->C:\Users\<USER>\AndroidStudioProjects\PigeonRingFwUpdateTool\app\src\main\AndroidManifest.xml:33:9-54
55        android:icon="@mipmap/ic_launcher"
55-->C:\Users\<USER>\AndroidStudioProjects\PigeonRingFwUpdateTool\app\src\main\AndroidManifest.xml:34:9-43
56        android:label="@string/app_name"
56-->C:\Users\<USER>\AndroidStudioProjects\PigeonRingFwUpdateTool\app\src\main\AndroidManifest.xml:35:9-41
57        android:roundIcon="@mipmap/ic_launcher_round"
57-->C:\Users\<USER>\AndroidStudioProjects\PigeonRingFwUpdateTool\app\src\main\AndroidManifest.xml:36:9-54
58        android:supportsRtl="true"
58-->C:\Users\<USER>\AndroidStudioProjects\PigeonRingFwUpdateTool\app\src\main\AndroidManifest.xml:37:9-35
59        android:theme="@style/Theme.PigeonRingFwUpdateTool" >
59-->C:\Users\<USER>\AndroidStudioProjects\PigeonRingFwUpdateTool\app\src\main\AndroidManifest.xml:38:9-60
60        <activity
60-->C:\Users\<USER>\AndroidStudioProjects\PigeonRingFwUpdateTool\app\src\main\AndroidManifest.xml:40:9-48:20
61            android:name="com.android.pigeonringfwupdatetool.MainActivity"
61-->C:\Users\<USER>\AndroidStudioProjects\PigeonRingFwUpdateTool\app\src\main\AndroidManifest.xml:41:13-41
62            android:exported="true" >
62-->C:\Users\<USER>\AndroidStudioProjects\PigeonRingFwUpdateTool\app\src\main\AndroidManifest.xml:42:13-36
63            <intent-filter>
63-->C:\Users\<USER>\AndroidStudioProjects\PigeonRingFwUpdateTool\app\src\main\AndroidManifest.xml:43:13-47:29
64                <action android:name="android.intent.action.MAIN" />
64-->C:\Users\<USER>\AndroidStudioProjects\PigeonRingFwUpdateTool\app\src\main\AndroidManifest.xml:44:17-69
64-->C:\Users\<USER>\AndroidStudioProjects\PigeonRingFwUpdateTool\app\src\main\AndroidManifest.xml:44:25-66
65
66                <category android:name="android.intent.category.LAUNCHER" />
66-->C:\Users\<USER>\AndroidStudioProjects\PigeonRingFwUpdateTool\app\src\main\AndroidManifest.xml:46:17-77
66-->C:\Users\<USER>\AndroidStudioProjects\PigeonRingFwUpdateTool\app\src\main\AndroidManifest.xml:46:27-74
67            </intent-filter>
68        </activity>
69
70        <provider
70-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dc95fa0300ba58a6aceeca47001372b8\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
71            android:name="androidx.startup.InitializationProvider"
71-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dc95fa0300ba58a6aceeca47001372b8\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
72            android:authorities="com.android.pigeonringfwupdatetool.androidx-startup"
72-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dc95fa0300ba58a6aceeca47001372b8\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
73            android:exported="false" >
73-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dc95fa0300ba58a6aceeca47001372b8\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
74            <meta-data
74-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dc95fa0300ba58a6aceeca47001372b8\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
75                android:name="androidx.emoji2.text.EmojiCompatInitializer"
75-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dc95fa0300ba58a6aceeca47001372b8\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
76                android:value="androidx.startup" />
76-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\dc95fa0300ba58a6aceeca47001372b8\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
77            <meta-data
77-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\165c81edd120775bfe2e19f482a1ce1f\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
78                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
78-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\165c81edd120775bfe2e19f482a1ce1f\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
79                android:value="androidx.startup" />
79-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\165c81edd120775bfe2e19f482a1ce1f\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
80            <meta-data
80-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b224472f1025f1b9ec1009528c4b4ca6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
81                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
81-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b224472f1025f1b9ec1009528c4b4ca6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
82                android:value="androidx.startup" />
82-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b224472f1025f1b9ec1009528c4b4ca6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
83        </provider>
84
85        <receiver
85-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b224472f1025f1b9ec1009528c4b4ca6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
86            android:name="androidx.profileinstaller.ProfileInstallReceiver"
86-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b224472f1025f1b9ec1009528c4b4ca6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
87            android:directBootAware="false"
87-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b224472f1025f1b9ec1009528c4b4ca6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
88            android:enabled="true"
88-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b224472f1025f1b9ec1009528c4b4ca6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
89            android:exported="true"
89-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b224472f1025f1b9ec1009528c4b4ca6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
90            android:permission="android.permission.DUMP" >
90-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b224472f1025f1b9ec1009528c4b4ca6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
91            <intent-filter>
91-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b224472f1025f1b9ec1009528c4b4ca6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
92                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
92-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b224472f1025f1b9ec1009528c4b4ca6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
92-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b224472f1025f1b9ec1009528c4b4ca6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
93            </intent-filter>
94            <intent-filter>
94-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b224472f1025f1b9ec1009528c4b4ca6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
95                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
95-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b224472f1025f1b9ec1009528c4b4ca6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
95-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b224472f1025f1b9ec1009528c4b4ca6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
96            </intent-filter>
97            <intent-filter>
97-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b224472f1025f1b9ec1009528c4b4ca6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
98                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
98-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b224472f1025f1b9ec1009528c4b4ca6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
98-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b224472f1025f1b9ec1009528c4b4ca6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
99            </intent-filter>
100            <intent-filter>
100-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b224472f1025f1b9ec1009528c4b4ca6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
101                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
101-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b224472f1025f1b9ec1009528c4b4ca6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
101-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b224472f1025f1b9ec1009528c4b4ca6\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
102            </intent-filter>
103        </receiver>
104    </application>
105
106</manifest>
