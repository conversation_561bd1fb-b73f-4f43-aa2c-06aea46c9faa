# Hex檔案讀取功能使用說明

## 功能概述
已在韌體更新流程中實作了讀取hex檔案並將HexString打印到Android Studio debug console的功能。

## 使用方法

### 1. 選擇hex檔案
- 點擊 "Select Hex File" 按鈕
- 使用檔案選擇器選擇一個.hex檔案
- 檔案會顯示在hex files列表中

### 2. 連接設備
- 點擊 "Bluetooth Connect" 按鈕連接到鴿環設備
- 確保設備已成功連接

### 3. 開始韌體更新並查看hex內容
- 點擊 "Update Firmware" 按鈕開始韌體更新
- 系統會自動讀取並解析hex檔案
- hex內容會詳細打印到Android Studio的debug console中

### 3. 查看debug輸出
在Android Studio的Logcat中，使用以下filter查看輸出：
- Tag: `mickey` (這是MainActivity中設定的TAG)
- 搜尋關鍵字: `HEX FILE CONTENT` 或 `HEX STRINGS`

## 輸出格式

### 基本資訊
```
=== HEX FILE CONTENT START ===
Source: [檔案來源]
=== HEX STRINGS ===
```

### 每行hex記錄
```
Line 0001: :020000040000FA
        -> Bytes: 02, Addr: 0000, Type: 04 (Extended Linear Address), Checksum: FA

Line 0002: :10000000C00E00200D0100081101000811010008F4
        -> Bytes: 10, Addr: 0000, Type: 00 (Data), Checksum: F4
        -> Data: C0 0E 00 20 0D 01 00 08 11 01 00 08 11 01 00 08
```

### 結束資訊
```
=== HEX FILE CONTENT END ===
Total lines processed: [總行數]
Total characters: [總字元數]
=== HEX FILE READING COMPLETE ===
```

## 支援的hex記錄類型
- 0x00: Data (資料記錄)
- 0x01: End of File (檔案結束)
- 0x02: Extended Segment Address (擴展段地址)
- 0x03: Start Segment Address (起始段地址)
- 0x04: Extended Linear Address (擴展線性地址)
- 0x05: Start Linear Address (起始線性地址)

## 測試檔案
專案根目錄中包含一個測試檔案 `test_firmware.hex`，您可以將此檔案複製到手機的Downloads資料夾中進行測試。

## 錯誤處理
- 如果沒有選擇hex檔案，會顯示提示訊息
- 如果檔案讀取失敗，會在console和Toast中顯示錯誤訊息
- 無效的hex格式會在console中顯示警告

## 注意事項
1. 確保已授予應用程式讀取儲存空間的權限
2. 長按按鈕才會觸發讀取功能，普通點擊仍然是檔案選擇功能
3. 大型hex檔案可能會產生大量log輸出，請注意Logcat的緩衝區限制
