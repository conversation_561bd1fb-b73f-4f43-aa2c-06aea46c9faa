package com.android.flightplanapp.cmd;

import static com.android.flightplanapp.cmd.AFPCommand.AnalyzeReceivedata;
import static com.android.flightplanapp.cmd.AFPCommand.Checksum;
import static com.android.flightplanapp.utils.Parse.byteToHex;
import static com.android.flightplanapp.utils.Parse.bytesToHex;

import android.util.Log;

import org.apache.commons.lang3.ArrayUtils;

import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class Command0002 extends CommandBase {

    static String TAG = "LeDa";

    public static boolean CheckResponseData(byte[] send,byte[] recv) {
        short Opcode = (short)(((int)send[6]) * 256 + send[7] + 0x8000);
        if (AnalyzeReceivedata(recv, send[4], send[3], send[5], Opcode) == false) {
            return false;
        }
        return true;
    }

    static int MEMORY_CONTROL_OFFECT = 8;
    static int MEMORY_LENGTH_OFFSET = 13;
    static int MEMORY_DATA_OFFSET = 14;

    public Command0002(byte devicedid, byte src)
    {
        header[0] = (byte) 0xff;    //SYNC
        header[1] = (byte) 0x00;    //Message Length
        header[2] = (byte) 0x07;    //Flag
        header[3] = (byte) 0x01;    //Source ID
        header[4] = devicedid;      //Destination ID
        header[5] = src;            //SquenceNumber
        header[6] = (byte) 0x00;    //Opcode
        header[7] = 0x02;
    }
    byte memcontrol = 0;
    long startaddress = 0;
    byte datalength = 0;
    byte[] data = new byte[0];
    public enum MemControlType {
        ERASE(1),
        READ(2),
        WRITE(3),
        APP_INFO(4);

        private final int value;

        MemControlType(int value) {
            this.value = value;
        }

        public int getValue() {
            return value;
        }

        public static MemControlType fromValue(int value) {
            return Arrays.stream(values())
                    .filter(type -> type.getValue() == value)
                    .findFirst()
                    .orElse(null); // 找不到時返回 null
        }
    }

//    public enum MemControlType
//    {
//        Erase = 1,
//        Read,
//        Write,
//        APPInfo
//    }

    public void SetMemControl(MemControlType memcontrolType)
    {
        memcontrol = (byte)memcontrolType.getValue();
    }
    public enum MemStructureType
    {
        EndAddress(1),
        DataLen(2);
        private final int value;
        MemStructureType(int i) {
            this.value = i;
        }

        public int getValue() {
            return value;
        }
    }
    public void SetStartAddress(long startaddress)
    {
        this.startaddress = startaddress;
    }
    public void SetDataLength(byte lenth)
    {
        this.datalength = lenth;
    }
    public boolean SetWriteData(byte[] data)
    {
        if (data.length <= 32)
//        if (data.length <= 128)
        {
            this.data = data;
            return true;
        }
        return false;
    }
    public byte[] GetSendData()
    {
        List<Byte> pollingdata = new ArrayList<Byte>();
        pollingdata.addAll(Arrays.asList(ArrayUtils.toObject(header))); // 0~7
        pollingdata.add(memcontrol); // 8, 0x3 = write
//        byte[] bstmp = BitConverter.GetBytes(startaddress);
        byte[] bstmp = ByteBuffer.allocate(4).putInt((int) startaddress).array();
//        Log.e("LeDa", "1 >>>> " + bytesToHex(bstmp, bstmp.length));
//        for (int i = 3; i >= 0; i--) {
        for (int i = 0; i < bstmp.length; i++) {
            pollingdata.add(bstmp[i]);
        }
        if (memcontrol == (byte)MemControlType.WRITE.getValue() || memcontrol == (byte)MemControlType.APP_INFO.getValue())
        {
            pollingdata.add((byte)data.length);
            for (byte b : data) { // 將 data 每個 byte 加入
                pollingdata.add(b);
            }
        }
        else
        {
            pollingdata.add(datalength);
        }
        pollingdata.set(1, (byte) (pollingdata.size() - 1));
        pollingdata.add(Checksum(listToByte(pollingdata)));
        return listToByte(pollingdata);
    }

    public static boolean getMemoryControl(byte[] data, MemControlType[] memControlType) {
        if (memControlType == null || memControlType.length < 1) {
            throw new IllegalArgumentException("memControlType must be a valid array with at least one element.");
        }

        try {
            int value = data[MEMORY_CONTROL_OFFECT] & 0xFF; // 將 byte 轉為無符號 int
            memControlType[0] = MemControlType.fromValue(value);
            return memControlType[0] != null; // 如果值無效，返回 false
        } catch (IndexOutOfBoundsException e) {
            memControlType[0] = null;
            return false;
        }
    }

    /* not used
    public static boolean GetDataLength(byte[] data, out byte memDataLength)
    {
        memDataLength = 0x00;
        try
        {
            memDataLength = data[MEMORY_LENGTH_OFFSET];
            return true;
        }
        catch
        {
            return false;
        }
    }
    */

    /* org code
    public static boolean GetMemoryData(byte[] data, out byte[] memData)
    {
        memData = new byte[1] { 0x00 };
        try
        {
            int memDataLength = data[MEMORY_LENGTH_OFFSET];
            Array.Resize(ref memData, memDataLength);
            Array.Copy(data, MEMORY_DATA_OFFSET, memData, 0, memDataLength);
            return true;
        }
        catch
        {
            return false;
        }
    }
    */

    public static boolean GetMemoryData(byte[] data)
    {
        try {
            // 獲取記憶體數據長度
            int memDataLength = data[MEMORY_LENGTH_OFFSET] & 0xFF; // 無符號轉換
            Log.d(TAG, "CMD: 0002 memDataLength : " + memDataLength + ", return true");
            // 初始化 memData.data 為正確大小
            data = new byte[memDataLength];
            // 複製數據到 memData.data
            //System.arraycopy(來源, 起始索引, 目的, 起始索引, 複製長度)
            System.arraycopy(data, MEMORY_DATA_OFFSET, data, 0, memDataLength);
            return true;
        } catch (Exception e) {
            System.out.println("CMD: 0002memDataLength : return false >>> " + e);
            data = new byte[]{0x00};
            return false;
        }
    }
}
