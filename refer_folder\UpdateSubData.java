package com.android.flightplanapp.updateutils;

import java.util.List;

public class UpdateSubData {
    public long addressoffect;
    public List<Byte> bindatas;

    public UpdateSubData(long addressoffect, List<Byte> bindatas) {
        this.addressoffect = addressoffect;
        this.bindatas = bindatas;
    }

    // Getter 方法
    public long getAddressEffect() {
        return addressoffect;
    }

    public List<Byte> getBinDatas() {
        return bindatas;
    }
}
