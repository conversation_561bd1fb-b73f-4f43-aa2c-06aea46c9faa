<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/available_devices"
        android:textSize="18sp"
        android:textStyle="bold"
        android:layout_marginBottom="16dp" />

    <ProgressBar
        android:id="@+id/progress_scanning"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginBottom="16dp" />

    <TextView
        android:id="@+id/tv_scanning"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/scanning"
        android:layout_gravity="center"
        android:layout_marginBottom="16dp" />

    <ListView
        android:id="@+id/lv_available_devices"
        android:layout_width="match_parent"
        android:layout_height="300dp"
        android:divider="#CCCCCC"
        android:dividerHeight="1dp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginTop="16dp">

        <Button
            android:id="@+id/btn_cancel"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="取消"
            android:layout_marginEnd="8dp" />

        <Button
            android:id="@+id/btn_refresh"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="重新掃描"
            android:layout_marginStart="8dp" />

    </LinearLayout>

</LinearLayout>