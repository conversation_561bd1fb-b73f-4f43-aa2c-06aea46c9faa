package com.android.flightplanapp.utils;

import static com.android.flightplanapp.device.Config.CMD_0001;
import static com.android.flightplanapp.device.Config.CMD_0002;
import static com.android.flightplanapp.device.Config.CMD_0011;
import static com.android.flightplanapp.device.Config.CMD_0012;
import static com.android.flightplanapp.device.Config.CMD_0013;
import static com.android.flightplanapp.device.Config.CMD_0081;
import static com.android.flightplanapp.device.Config.CMD_0081_DEL_DATA;
import static com.android.flightplanapp.device.Config.CMD_00FA;
import static com.android.flightplanapp.device.Config.CMD_CHECK_HEX_FILE;
import static com.android.flightplanapp.device.Config.CMD_GET_OP_MODE;
import static com.android.flightplanapp.device.Config.CMD_QUERY_FW_VER;
import static com.android.flightplanapp.device.Config.CMD_SWITCH_APP_MODE;
import static com.android.flightplanapp.device.Config.CMD_SWITCH_BOOTLOADER;
import static com.android.flightplanapp.device.Config.CMD_UPGRADE_FW;
import static com.android.flightplanapp.utils.Parse.byteToHex;
import static com.android.flightplanapp.utils.Parse.bytesToHex;

import android.util.Log;
import com.android.flightplanapp.cmd.Command0001;
import com.android.flightplanapp.cmd.Command0002;
import com.android.flightplanapp.cmd.Command0011;
import com.android.flightplanapp.cmd.Command0012;
import com.android.flightplanapp.cmd.Command0013;
import com.android.flightplanapp.cmd.Command0081;
import com.android.flightplanapp.cmd.Command00fa;
import com.android.flightplanapp.updateutils.UpdateSubData;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

public class CommandExecutor {

    private static final String TAG = "CommandExecutor";
    private static Calendar dateTime = Calendar.getInstance();

    public static byte[] execute(String command, byte charging_src) {
        switch (command) {
            case CMD_0001: // Firmware Update
                return executeCommand0001(charging_src);
            case CMD_0002: // Firmware Update
                return executeCommand0002(charging_src);
            case CMD_0011: // Fly Configuration
                return executeCommand0011(charging_src);
            case CMD_0012: // RTC
                return executeCommand0012(charging_src);
            case CMD_0013:
                return executeCommand0013(charging_src);
            case CMD_0081: // Fly Data
                return executeCommand0081(charging_src);
            case CMD_00FA: // Fly Data
                return executeCommand00fa(charging_src);
            case CMD_0081_DEL_DATA:
                return executeCommand0081_removeFlyData(charging_src);
            case CMD_QUERY_FW_VER:
                return executeQueryFwVer(charging_src);
            case CMD_SWITCH_BOOTLOADER:
                return executeSwitchBootLoader(charging_src);
            case CMD_SWITCH_APP_MODE:
                return executeSwitchAppMode(charging_src);
            case CMD_GET_OP_MODE:
                return executeGetOpMode(charging_src);
//            case CMD_UPGRADE_FW:
//                return executeUpgradeFw(charging_src);
            default:
                Log.e(TAG, "Unknown command: " + command);
                return null;
        }
    }

    private static byte[] executeCommand00fa(byte chargingSrc) {
        Command00fa command00fa = new Command00fa((byte) 0x03, chargingSrc);
        byte[] send;
        send = command00fa.GetSendData();
        return send;
    }

    private static byte[] executeGetOpMode(byte chargingSrc) {
        Command0001 command0001 = new Command0001((byte) 0x03, chargingSrc);
        command0001.ClearAllRequest();
        command0001.SetRequestOpmode(true);
        byte[] send;
        send = command0001.GetSendData();
        return send;
    }

    private static byte[] executeSwitchAppMode(byte chargingSrc) {
        Command0001 command0001 = new Command0001((byte) 0x03, chargingSrc);
        Command0002 command0002 = new Command0002((byte) 0x03, chargingSrc);
        command0001.ClearAllRequest();
        command0001.SetRequestJumpToApp(true);
        byte[] send;
        send = command0001.GetSendData();
        return send;
    }

    private static byte[] executeSwitchBootLoader(byte chargingSrc) {
        Command0001 command0001 = new Command0001((byte) 0x03, chargingSrc);
        Command0002 command0002 = new Command0002((byte) 0x03, chargingSrc);
        command0001.ClearAllRequest();
        command0001.SetRequestJumpToBootloader(true);
        byte[] send;
        send = command0001.GetSendData();
        return send;
    }

    private static byte[] executeQueryFwVer(byte chargingSrc) {
        Command0001 command0001 = new Command0001((byte) 0x03, chargingSrc);
        Command0002 command0002 = new Command0002((byte) 0x03, chargingSrc);
//        byte[] datatest = new byte[64];
//        for (byte i = 0; i < 64; i++)
//        {
//            datatest[i] = i;
//        }
        byte[] send;
        command0001.SetQueryQueryFirmwareVersion(true);
        send = command0001.GetSendData();
        return send;
    }

    private static byte[] executeCommand0001(byte charging_src) {
        Command0001 command0001 = new Command0001((byte) 0x03, charging_src);
        byte[] datatest = new byte[64];
        for (byte i = 0; i < 64; i++)
        {
            datatest[i] = i;
        }
        byte[] send;
        command0001.SetQueryQueryFirmwareVersion(true);
        send = command0001.GetSendData();
        return send;
    }

    private static byte[] executeCommand0002(byte chargingSrc) {
        Command0002 command0002 = new Command0002((byte) 0x03, chargingSrc);
        byte[] send;
        send = command0002.GetSendData();
        return send;
    }

    private static byte[] executeCommand0011(byte charging_src) {
        Command0011 command0011 = new Command0011((byte) 0x03, charging_src);
        command0011.SetSecondInterval();
        command0011.SetDefaultInterval();
        command0011.SetSecondArea();
        command0011.SetConfigTime(dateTime);
        byte[] send;
        command0011.SetQueryOrUpdate(true);
        send = command0011.GetSendData();
        return send;
    }

    private static byte[] executeCommand0012(byte charging_src) {
        Command0012 command0012 = new Command0012((byte) 0x03, charging_src);
        byte[] send;
        command0012.SetQueryOrUpdate(true);
        send = command0012.GetSendData();
        return send;
    }

    private static byte[] executeCommand0013(byte charging_src) {
        Command0013 command0013 = new Command0013((byte) 0x03, charging_src);
        byte[] send;
        send = command0013.GetSendData();
        return send;
    }

    private static byte[] executeCommand0081(byte charging_src) {
        Command0081 command0081 = new Command0081((byte) 0x03, charging_src);
        byte[] send;
        command0081.QueryHowmanyDataSave(true);
        send = command0081.GetSendData();
        return send;
    }

    private static byte[] executeCommand0081_removeFlyData(byte charging_src) {
        Command0081 command0081 = new Command0081((byte) 0x03, charging_src);
        byte[] send;
        command0081.RequestRemoveFlyData(true);
        send = command0081.GetSendData();
        return send;
    }

    private static byte[] executeUpgradeFw(byte chargingSrc) {
        Command0001 command0001 = new Command0001((byte) 0x03, chargingSrc);
        Command0002 command0002 = new Command0002((byte) 0x03, chargingSrc);
        byte[] send;
        send = command0001.GetSendData();
        return send;
    }
}
