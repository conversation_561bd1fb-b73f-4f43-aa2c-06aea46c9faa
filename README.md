# 鴿環韌體更新工具 (Pigeon Ring Firmware Update Tool)

## 專案概述
這是一個 Android 應用程式，用於透過藍芽 BLE GATT 連線更新鴿環裝置的韌體。

## 已實現功能

### 1. 藍芽連線功能
- ✅ 藍芽權限管理 (支援 Android 12+ 新權限)
- ✅ 藍芽連線按鈕
- ✅ 藍芽掃描對話框 (Dialog)
- ✅ 顯示附近可用藍芽裝置
- ✅ 用戶點選裝置進行配對連線
- ✅ 連線成功後自動關閉對話框
- ✅ 已配對裝置列表顯示 (ListView)

### 2. 裝置管理
- ✅ 點選已配對裝置進行藍芽連線
- ✅ 連線狀態管理 (連線/斷線)
- ✅ 更新按鈕狀態控制 (連線成功後啟用)

### 3. Hex 檔案管理
- ✅ 檢查 Downloads 資料夾中的 Hex 檔案
- ✅ 顯示掃描到的 Hex 檔案列表 (ListView)
- ✅ 點擊選擇 Hex 檔案並綁定路徑

### 4. 權限管理
- ✅ 藍芽相關權限 (BLUETOOTH_SCAN, BLUETOOTH_CONNECT 等)
- ✅ 檔案存取權限 (READ_EXTERNAL_STORAGE, MANAGE_EXTERNAL_STORAGE)
- ✅ 位置權限 (ACCESS_FINE_LOCATION, ACCESS_COARSE_LOCATION)
- ✅ 動態權限請求處理

## 技術規格
- **最低 Android 版本**: API 24 (Android 7.0)
- **目標 Android 版本**: API 35 (Android 15)
- **藍芽技術**: BLE GATT
- **檔案格式**: Intel Hex (.hex)
- **預設檔案路徑**: /storage/emulated/0/Download/

## 專案結構
```
app/src/main/
├── java/com/android/pigeonringfwupdatetool/
│   └── MainActivity.java                 # 主要活動類別
├── res/
│   ├── layout/
│   │   ├── activity_main.xml            # 主介面佈局
│   │   ├── dialog_bluetooth_scan.xml    # 藍芽掃描對話框
│   │   └── list_item_device.xml         # 裝置列表項目
│   ├── values/
│   │   ├── strings.xml                  # 字串資源
│   │   ├── colors.xml                   # 顏色資源
│   │   └── themes.xml                   # 主題樣式
│   └── xml/
│       ├── data_extraction_rules.xml
│       └── backup_rules.xml
└── AndroidManifest.xml                  # 應用程式清單
```

## 待實現功能
1. 韌體更新邏輯 (需要具體的更新協議)
2. 進度顯示
3. 錯誤處理和重試機制
4. 更新日誌記錄

## 使用說明
1. 啟動應用程式
2. 點擊「Bluetooth Connect」搜尋並連線藍芽裝置
3. 或從已配對裝置列表中選擇裝置連線
4. 點擊「Check Hex File」掃描 Downloads 資料夾中的 Hex 檔案
5. 選擇要更新的 Hex 檔案
6. 連線成功且選擇檔案後，「Update Firmware」按鈕將啟用
7. 點擊更新按鈕開始韌體更新 (待實現)

## 注意事項
- 首次使用需要授予藍芽和檔案存取權限
- Android 11+ 需要在設定中手動授予「管理所有檔案」權限
- 確保 Hex 檔案放置在 Downloads 資料夾中