<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp">

    <!-- Bluetooth connection area -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:orientation="horizontal">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/paired_devices"
            android:textSize="18sp"
            android:textStyle="bold" />

        <Button
            android:id="@+id/btn_refresh_paired"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="8dp"
            android:text="Refresh"
            android:textSize="12sp" />
    </LinearLayout>

    <Button
        android:id="@+id/btn_bluetooth_connect"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:text="@string/bluetooth_connect" />

    <ListView
        android:id="@+id/lv_paired_devices"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginBottom="16dp"
        android:layout_weight="1"
        android:divider="#CCCCCC"
        android:dividerHeight="1dp" />

    <!-- Hex file check area -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:text="@string/hex_files"
        android:textSize="18sp"
        android:textStyle="bold" />

    <Button
        android:id="@+id/btn_check_hex"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:text="@string/check_hex_file" />

    <ListView
        android:id="@+id/lv_hex_files"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginBottom="16dp"
        android:layout_weight="1"
        android:divider="#CCCCCC"
        android:dividerHeight="1dp" />

    <!-- Update button -->
    <Button
        android:id="@+id/btn_update_firmware"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:enabled="false"
        android:text="@string/update_firmware"
        android:textSize="16sp" />

</LinearLayout>