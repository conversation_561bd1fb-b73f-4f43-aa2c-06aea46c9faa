package com.android.pigeonringfwupdatetool;

import android.Manifest;
import android.app.AlertDialog;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothGatt;
import android.bluetooth.BluetoothGattCallback;
import android.bluetooth.BluetoothGattCharacteristic;
import android.bluetooth.BluetoothGattDescriptor;
import android.bluetooth.BluetoothGattService;
import android.bluetooth.BluetoothManager;
import android.bluetooth.BluetoothProfile;
import android.bluetooth.le.BluetoothLeScanner;
import android.bluetooth.le.ScanCallback;
import android.bluetooth.le.ScanFilter;
import android.bluetooth.le.ScanResult;
import android.bluetooth.le.ScanSettings;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.PackageManager;
import static android.Manifest.permission.ACCESS_COARSE_LOCATION;
import static android.Manifest.permission.ACCESS_FINE_LOCATION;
import static android.Manifest.permission.BLUETOOTH_CONNECT;
import static android.Manifest.permission.BLUETOOTH_SCAN;
import static android.Manifest.permission.READ_EXTERNAL_STORAGE;
import static android.Manifest.permission.WRITE_EXTERNAL_STORAGE;
import android.os.Build;
import android.os.Bundle;
import android.os.Environment;
import android.os.Handler;
import android.util.Log;
import android.content.ContentResolver;
import android.database.Cursor;
import android.net.Uri;
import android.provider.MediaStore;
import android.provider.DocumentsContract;
import android.app.Activity;
import java.io.InputStream;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.ListView;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

public class MainActivity extends AppCompatActivity {

    String TAG = "mickey";
    private static final int REQUEST_BLUETOOTH_PERMISSIONS = 1001;
    private static final int REQUEST_STORAGE_PERMISSIONS = 1002;
    private static final int REQUEST_ENABLE_BT = 1003;
    private static final int PERMISSION_REQUEST_CODE = 1004;
    private static final int READ_REQUEST_CODE = 1005;
    private static final long SCAN_PERIOD = 10000; // 10 seconds

    private BluetoothAdapter bluetoothAdapter;
    private BluetoothLeScanner bluetoothLeScanner;
    private BluetoothGatt bluetoothGatt;
    private Handler handler = new Handler();
    private boolean scanning = false;

    // UI Components
    private Button btnBluetoothConnect;
    private Button btnCheckHex;
    private Button btnUpdateFirmware;
    private Button btnRefreshPaired;
    private ListView lvPairedDevices;
    private ListView lvHexFiles;

    // Data
    private ArrayList<BluetoothDevice> pairedDevicesList = new ArrayList<>();
    private ArrayList<BluetoothDevice> availableDevicesList = new ArrayList<>();
    private ArrayList<BluetoothDevice> connectedDevicesList = new ArrayList<>(); // For BLE devices
    private ArrayList<String> hexFilesList = new ArrayList<>();
    private ArrayAdapter<String> pairedDevicesAdapter;
    private ArrayAdapter<String> hexFilesAdapter;
    private String selectedHexFilePath = "";
    private BluetoothDevice connectedDevice = null;
    private ArrayAdapter<String> currentAdapter; // Keep reference to current adapter

    // Broadcast receiver for pairing events
    private final BroadcastReceiver pairingReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            if (BluetoothDevice.ACTION_BOND_STATE_CHANGED.equals(action)) {
                BluetoothDevice device = intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE);
                int bondState = intent.getIntExtra(BluetoothDevice.EXTRA_BOND_STATE, BluetoothDevice.BOND_NONE);
                
                switch (bondState) {
                    case BluetoothDevice.BOND_BONDED:
                        Toast.makeText(MainActivity.this, "Device paired successfully! Now connecting...", Toast.LENGTH_LONG).show();
                        Log.d(TAG, "Device paired successfully: " + device.getAddress());
                        
                        // Add a small delay before refreshing the list to ensure pairing is complete
                        handler.postDelayed(() -> {
                            Log.d("BLE", "Pairing completed, refreshing paired devices list");
                            loadPairedDevices(); // Refresh the paired devices list
                            Toast.makeText(MainActivity.this, "Paired devices list updated", Toast.LENGTH_SHORT).show();
                        }, 2000); // Increase delay to 2 seconds
                        
                        // Now try to connect to the newly paired device
                        connectToDevice(device);
                        break;
                    case BluetoothDevice.BOND_BONDING:
                        Toast.makeText(MainActivity.this, "Pairing in progress...", Toast.LENGTH_SHORT).show();
                        Log.d(TAG, "Pairing in progress: " + device.getAddress());
                        break;
                    case BluetoothDevice.BOND_NONE:
                        Toast.makeText(MainActivity.this, "Pairing failed or cancelled", Toast.LENGTH_LONG).show();
                        Log.w(TAG, "Pairing failed: " + device.getAddress());
                        break;
                }
            }
        }
    };

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        initializeViews();
        initializeBluetooth();
        setupListeners();
        
        // Force permission check on startup
        forcePermissionCheck();
        
        // Register broadcast receiver for pairing events
        IntentFilter filter = new IntentFilter(BluetoothDevice.ACTION_BOND_STATE_CHANGED);
        registerReceiver(pairingReceiver, filter);
    }

    private void initializeViews() {
        btnBluetoothConnect = findViewById(R.id.btn_bluetooth_connect);
        btnCheckHex = findViewById(R.id.btn_check_hex);
        btnUpdateFirmware = findViewById(R.id.btn_update_firmware);
        btnRefreshPaired = findViewById(R.id.btn_refresh_paired);
        lvPairedDevices = findViewById(R.id.lv_paired_devices);
        lvHexFiles = findViewById(R.id.lv_hex_files);

        // Setup adapters
        pairedDevicesAdapter = new ArrayAdapter<>(this, android.R.layout.simple_list_item_1);
        lvPairedDevices.setAdapter(pairedDevicesAdapter);

        hexFilesAdapter = new ArrayAdapter<>(this, android.R.layout.simple_list_item_1);
        lvHexFiles.setAdapter(hexFilesAdapter);
    }

    private void initializeBluetooth() {
        BluetoothManager bluetoothManager = (BluetoothManager) getSystemService(Context.BLUETOOTH_SERVICE);
        bluetoothAdapter = bluetoothManager.getAdapter();

        if (bluetoothAdapter == null) {
            Toast.makeText(this, "Bluetooth not supported", Toast.LENGTH_SHORT).show();
            finish();
            return;
        }

        if (!bluetoothAdapter.isEnabled()) {
            Intent enableBtIntent = new Intent(BluetoothAdapter.ACTION_REQUEST_ENABLE);
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                if (ActivityCompat.checkSelfPermission(this, Manifest.permission.BLUETOOTH_CONNECT) == PackageManager.PERMISSION_GRANTED) {
                    startActivityForResult(enableBtIntent, REQUEST_ENABLE_BT);
                }
            } else {
                startActivityForResult(enableBtIntent, REQUEST_ENABLE_BT);
            }
        }

        if (bluetoothAdapter.isEnabled()) {
            bluetoothLeScanner = bluetoothAdapter.getBluetoothLeScanner();
            if (bluetoothLeScanner == null) {
                Toast.makeText(this, "BLE Scanner not available", Toast.LENGTH_SHORT).show();
            }
        }
    }

    private void setupListeners() {
        btnBluetoothConnect.setOnClickListener(v -> {
            // Always check permissions before showing scan dialog using your method
            if (!isPermissionsGot()) {
                Toast.makeText(this, "Please grant all required permissions first!", Toast.LENGTH_LONG).show();
                requestPermissions();
                return;
            }
            showBluetoothScanDialog();
        });
        
        btnRefreshPaired.setOnClickListener(v -> {
            Toast.makeText(this, "Refreshing paired devices...", Toast.LENGTH_SHORT).show();
            Log.d(TAG, "Manual refresh button clicked");
            
            // Debug current state
            Log.d(TAG, "Current adapter count: " + (pairedDevicesAdapter != null ? pairedDevicesAdapter.getCount() : "null"));
            Log.d(TAG, "ListView adapter set: " + (lvPairedDevices.getAdapter() != null));
            Log.d(TAG, "Permissions granted: " + isPermissionsGot());
            
            loadPairedDevices();
        });
        
        btnCheckHex.setOnClickListener(v -> {
            // Direct to manual file picker
            performFileSearch();
        });

        btnUpdateFirmware.setOnClickListener(v -> {
            startFirmwareUpdate();
        });
        
        lvPairedDevices.setOnItemClickListener((parent, view, position, id) -> {
            if (position < pairedDevicesList.size()) {
                BluetoothDevice device = pairedDevicesList.get(position);
                connectToDevice(device);
            }
        });

        lvHexFiles.setOnItemClickListener((parent, view, position, id) -> {
            if (position < hexFilesList.size()) {
                String fileName = hexFilesList.get(position);
                selectedHexFilePath = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS) + "/" + fileName;
                
                // Verify file exists and get details
                File selectedFile = new File(selectedHexFilePath);
                if (selectedFile.exists()) {
                    long fileSize = selectedFile.length();
                    String fileSizeStr = formatFileSize(fileSize);
                    
                    Toast.makeText(this, "Selected: " + fileName + "\nSize: " + fileSizeStr, Toast.LENGTH_LONG).show();
                    Log.d("HexScan", "Selected hex file: " + fileName + " at path: " + selectedHexFilePath);
                    Log.d("HexScan", "File size: " + fileSize + " bytes");
                } else {
                    Toast.makeText(this, "Error: File not found - " + fileName, Toast.LENGTH_LONG).show();
                    Log.e("HexScan", "Selected file does not exist: " + selectedHexFilePath);
                }
            }
        });
    }

    private void checkPermissions() {
        List<String> permissionsNeeded = new ArrayList<>();

        // Check Bluetooth permissions
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            if (ContextCompat.checkSelfPermission(this, Manifest.permission.BLUETOOTH_SCAN) != PackageManager.PERMISSION_GRANTED) {
                permissionsNeeded.add(Manifest.permission.BLUETOOTH_SCAN);
            }
            if (ContextCompat.checkSelfPermission(this, Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED) {
                permissionsNeeded.add(Manifest.permission.BLUETOOTH_CONNECT);
            }
            if (ContextCompat.checkSelfPermission(this, Manifest.permission.BLUETOOTH_ADVERTISE) != PackageManager.PERMISSION_GRANTED) {
                permissionsNeeded.add(Manifest.permission.BLUETOOTH_ADVERTISE);
            }
        }
        
        // Location permission is always needed for BLE scanning
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
            permissionsNeeded.add(Manifest.permission.ACCESS_FINE_LOCATION);
        }
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.ACCESS_COARSE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
            permissionsNeeded.add(Manifest.permission.ACCESS_COARSE_LOCATION);
        }

        // Check storage permissions
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            if (!Environment.isExternalStorageManager()) {
                Toast.makeText(this, "Please grant storage permission in settings", Toast.LENGTH_LONG).show();
            }
        } else {
            if (ContextCompat.checkSelfPermission(this, Manifest.permission.READ_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED) {
                permissionsNeeded.add(Manifest.permission.READ_EXTERNAL_STORAGE);
            }
        }

        if (!permissionsNeeded.isEmpty()) {
            String permissionsList = String.join(", ", permissionsNeeded);
            Toast.makeText(this, "Requesting permissions: " + permissionsList, Toast.LENGTH_LONG).show();
            Log.d(TAG, "Requesting permissions: " + permissionsList);
            ActivityCompat.requestPermissions(this, 
                permissionsNeeded.toArray(new String[0]), 
                REQUEST_BLUETOOTH_PERMISSIONS);
        } else {
            Toast.makeText(this, "All permissions already granted", Toast.LENGTH_SHORT).show();
            Log.d(TAG, "All permissions already granted --------------------------");
        }
    }

    private void loadPairedDevices() {
        Log.d(TAG, "=== Starting loadPairedDevices ===");
        
        // Check if bluetoothAdapter is available
        if (bluetoothAdapter == null) {
            Log.e(TAG, "BluetoothAdapter is null!");
            Toast.makeText(this, "Bluetooth adapter not available", Toast.LENGTH_LONG).show();
            return;
        }
        
        // Check if ListView and adapter are properly initialized
        if (lvPairedDevices == null) {
            Log.e(TAG, "ListView lvPairedDevices is null!");
            return;
        }
        
        if (pairedDevicesAdapter == null) {
            Log.e(TAG, "pairedDevicesAdapter is null!");
            return;
        }
        
        pairedDevicesList.clear();
        pairedDevicesAdapter.clear();
        Log.d(TAG, "Cleared existing lists");

        // Check permissions and load devices
        boolean hasPermission = false;
        Set<BluetoothDevice> pairedDevices = null;
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            hasPermission = checkSelfPermission(BLUETOOTH_CONNECT) == PackageManager.PERMISSION_GRANTED;
            Log.d(TAG, "Android 12+, BLUETOOTH_CONNECT permission: " + hasPermission);
            
            if (hasPermission) {
                try {
                    pairedDevices = bluetoothAdapter.getBondedDevices();
                    Log.d(TAG, "Successfully got bonded devices: " + (pairedDevices != null ? pairedDevices.size() : "null"));
                } catch (SecurityException e) {
                    Log.e(TAG, "SecurityException getting bonded devices: " + e.getMessage());
                    Toast.makeText(this, "Security error accessing paired devices", Toast.LENGTH_LONG).show();
                    return;
                }
            } else {
                Log.e(TAG, "BLUETOOTH_CONNECT permission not granted");
                Toast.makeText(this, "BLUETOOTH_CONNECT permission needed", Toast.LENGTH_LONG).show();
                return;
            }
        } else {
            Log.d(TAG, "Older Android version, no special permissions needed");
            try {
                pairedDevices = bluetoothAdapter.getBondedDevices();
                Log.d(TAG, "Successfully got bonded devices: " + (pairedDevices != null ? pairedDevices.size() : "null"));
                hasPermission = true;
            } catch (Exception e) {
                Log.e(TAG, "Exception getting bonded devices: " + e.getMessage());
                Toast.makeText(this, "Error accessing paired devices", Toast.LENGTH_LONG).show();
                return;
            }
        }
        
        // Process paired devices
        if (pairedDevices != null && hasPermission) {
            Log.d(TAG, "Processing " + pairedDevices.size() + " paired devices");
            
            if (pairedDevices.size() == 0) {
                Log.d(TAG, "No paired devices found in system");
                Toast.makeText(this, "No paired devices found. Please pair a device first.", Toast.LENGTH_LONG).show();
                
                // Add a test item to verify ListView is working
                pairedDevicesAdapter.add("No paired devices found\nPlease pair a device using system settings");
            } else {
                for (BluetoothDevice device : pairedDevices) {
                    try {
                        pairedDevicesList.add(device);
                        
                        String deviceName = "Unknown Device";
                        if (hasPermission) {
                            String name = device.getName();
                            deviceName = (name != null && !name.isEmpty()) ? name : "Unknown Device";
                        }
                        
                        String connectionStatus = (connectedDevice != null && connectedDevice.equals(device)) ? " [Connected]" : "";
                        String displayText = deviceName + connectionStatus + "\n" + device.getAddress();
                        
                        pairedDevicesAdapter.add(displayText);
                        Log.d(TAG, "Added device: " + displayText);
                        
                    } catch (Exception e) {
                        Log.e(TAG, "Error processing device: " + e.getMessage());
                    }
                }
            }
        } else {
            Log.e(TAG, "pairedDevices is null or no permission");
        }
        
        // Force UI update
        runOnUiThread(() -> {
            pairedDevicesAdapter.notifyDataSetChanged();
            Log.d(TAG, "UI updated - Adapter count: " + pairedDevicesAdapter.getCount());
            Log.d(TAG, "ListView adapter: " + (lvPairedDevices.getAdapter() != null ? "Set" : "NULL"));
            Log.d(TAG, "ListView child count: " + lvPairedDevices.getChildCount());
        });
        
        Log.d(TAG, "=== Finished loadPairedDevices ===");
    }
    
    private void addToConnectedDevicesList(BluetoothDevice device) {
        if (!connectedDevicesList.contains(device)) {
            connectedDevicesList.add(device);
            Log.d("BLE", "Added device to connected list: " + device.getAddress());
            
            // Update the paired devices list to show connected BLE devices
            updatePairedDevicesWithConnected();
        }
    }
    
    private void removeFromConnectedDevicesList(BluetoothDevice device) {
        if (connectedDevicesList.contains(device)) {
            connectedDevicesList.remove(device);
            Log.d("BLE", "Removed device from connected list: " + device.getAddress());
            
            // Update the paired devices list
            updatePairedDevicesWithConnected();
        }
    }
    
    private void updatePairedDevicesWithConnected() {
        // Clear and reload the list including both paired and connected BLE devices
        pairedDevicesList.clear();
        pairedDevicesAdapter.clear();
        
        // Add system paired devices
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            if (checkSelfPermission(BLUETOOTH_CONNECT) == PackageManager.PERMISSION_GRANTED) {
                Set<BluetoothDevice> pairedDevices = bluetoothAdapter.getBondedDevices();
                for (BluetoothDevice device : pairedDevices) {
                    pairedDevicesList.add(device);
                    String deviceName = device.getName() != null ? device.getName() : "Unknown Device";
                    String connectionStatus = (connectedDevice != null && connectedDevice.equals(device)) ? " [Connected]" : "";
                    pairedDevicesAdapter.add(deviceName + connectionStatus + "\n" + device.getAddress());
                }
            }
        }
        
        // Add connected BLE devices (that might not be paired)
        for (BluetoothDevice device : connectedDevicesList) {
            if (!pairedDevicesList.contains(device)) {
                pairedDevicesList.add(device);
                String deviceName = "Unknown Device";
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                    if (checkSelfPermission(BLUETOOTH_CONNECT) == PackageManager.PERMISSION_GRANTED) {
                        deviceName = device.getName() != null ? device.getName() : "BLE Device";
                    }
                } else {
                    deviceName = device.getName() != null ? device.getName() : "BLE Device";
                }
                String connectionStatus = (connectedDevice != null && connectedDevice.equals(device)) ? " [Connected]" : " [BLE Connected]";
                pairedDevicesAdapter.add(deviceName + connectionStatus + "\n" + device.getAddress());
            }
        }
        
        if (pairedDevicesList.isEmpty()) {
            pairedDevicesAdapter.add("No devices found\nConnect to a BLE device to see it here");
        }
        
        pairedDevicesAdapter.notifyDataSetChanged();
        Log.d("BLE", "Updated device list - Total devices: " + pairedDevicesList.size());
    }
    
    
    /**
     * Launch file picker using Storage Access Framework
     */
    private void performFileSearch() {
        Intent intent = new Intent(Intent.ACTION_OPEN_DOCUMENT);
        intent.addCategory(Intent.CATEGORY_OPENABLE);
        intent.setType("*/*"); // Allow any file type
        
        // Try to guide user to Downloads directory (may not work on all devices)
        try {
            intent.putExtra(DocumentsContract.EXTRA_INITIAL_URI, 
                Uri.parse("content://com.android.providers.downloads.documents/document/downloads"));
        } catch (Exception e) {
            Log.d("FilePicker", "Could not set initial directory: " + e.getMessage());
        }
        
        Toast.makeText(this, "Please select a .hex file", Toast.LENGTH_LONG).show();
        startActivityForResult(intent, READ_REQUEST_CODE);
    }
    
    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        if (requestCode == READ_REQUEST_CODE && resultCode == Activity.RESULT_OK) {
            if (data != null && data.getData() != null) {
                Uri uri = data.getData();
                Log.d("FilePicker", "Selected file URI: " + uri.toString());
                handleSelectedFile(uri);
            }
        }
    }
    
    /**
     * Handle the selected hex file
     */
    private void handleSelectedFile(Uri uri) {
        try {
            // Get file name from URI
            String fileName = getFileNameFromUri(uri);
            Log.d("FilePicker", "Selected file name: " + fileName);
            
            // Check if it's a hex file
            if (fileName != null && fileName.toLowerCase().endsWith(".hex")) {
                // Store the URI for later use
                selectedHexFilePath = uri.toString();
                
                // Get file size
                long fileSize = getFileSizeFromUri(uri);
                String fileSizeStr = formatFileSize(fileSize);
                
                // Update the hex files list
                hexFilesList.clear();
                hexFilesAdapter.clear();
                hexFilesList.add(fileName);
                hexFilesAdapter.add(fileName + "\nSize: " + fileSizeStr + "\n[Manually Selected]");
                hexFilesAdapter.notifyDataSetChanged();
                
                Toast.makeText(this, "Selected: " + fileName + "\nSize: " + fileSizeStr, Toast.LENGTH_LONG).show();
                Log.d("FilePicker", "Successfully selected hex file: " + fileName + " (" + fileSizeStr + ")");

                // Immediately parse hex file to verify data
                parseHexFileImmediately(uri.toString(), fileName);

            } else {
                Toast.makeText(this, "Please select a .hex file", Toast.LENGTH_LONG).show();
                Log.w("FilePicker", "Selected file is not a .hex file: " + fileName);
            }
            
        } catch (Exception e) {
            Log.e("FilePicker", "Error handling selected file", e);
            Toast.makeText(this, "Error reading selected file: " + e.getMessage(), Toast.LENGTH_LONG).show();
        }
    }
    
    private String getFileNameFromUri(Uri uri) {
        String fileName = null;
        try {
            Cursor cursor = getContentResolver().query(uri, null, null, null, null);
            if (cursor != null && cursor.moveToFirst()) {
                int nameIndex = cursor.getColumnIndex(DocumentsContract.Document.COLUMN_DISPLAY_NAME);
                if (nameIndex >= 0) {
                    fileName = cursor.getString(nameIndex);
                }
                cursor.close();
            }
        } catch (Exception e) {
            Log.e("FilePicker", "Error getting file name", e);
        }
        
        if (fileName == null) {
            // Fallback: extract from URI path
            String path = uri.getPath();
            if (path != null) {
                fileName = path.substring(path.lastIndexOf('/') + 1);
            }
        }
        
        return fileName;
    }
    
    private long getFileSizeFromUri(Uri uri) {
        long fileSize = 0;
        try {
            Cursor cursor = getContentResolver().query(uri, null, null, null, null);
            if (cursor != null && cursor.moveToFirst()) {
                int sizeIndex = cursor.getColumnIndex(DocumentsContract.Document.COLUMN_SIZE);
                if (sizeIndex >= 0) {
                    fileSize = cursor.getLong(sizeIndex);
                }
                cursor.close();
            }
        } catch (Exception e) {
            Log.e("FilePicker", "Error getting file size", e);
        }
        return fileSize;
    }

    private void showBluetoothScanDialog() {
        // Debug: Check current permissions and bluetooth state
        debugBluetoothState();
        
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        View dialogView = LayoutInflater.from(this).inflate(R.layout.dialog_bluetooth_scan, null);
        builder.setView(dialogView);

        AlertDialog dialog = builder.create();
        
        ListView lvAvailableDevices = dialogView.findViewById(R.id.lv_available_devices);
        ProgressBar progressScanning = dialogView.findViewById(R.id.progress_scanning);
        TextView tvScanning = dialogView.findViewById(R.id.tv_scanning);
        Button btnCancel = dialogView.findViewById(R.id.btn_cancel);
        Button btnRefresh = dialogView.findViewById(R.id.btn_refresh);

        ArrayAdapter<String> availableDevicesAdapter = new ArrayAdapter<>(this, android.R.layout.simple_list_item_1);
        lvAvailableDevices.setAdapter(availableDevicesAdapter);

        btnCancel.setOnClickListener(v -> {
            stopBluetoothScan();
            dialog.dismiss();
        });

        btnRefresh.setOnClickListener(v -> {
            availableDevicesList.clear();
            availableDevicesAdapter.clear();
            startBluetoothScan(availableDevicesAdapter, progressScanning, tvScanning);
        });

        lvAvailableDevices.setOnItemClickListener((parent, view, position, id) -> {
            if (position < availableDevicesList.size()) {
                BluetoothDevice device = availableDevicesList.get(position);
                
                // Show device info before connecting
                String deviceInfo = "Connecting to: ";
                if (ActivityCompat.checkSelfPermission(MainActivity.this, Manifest.permission.BLUETOOTH_CONNECT) == PackageManager.PERMISSION_GRANTED) {
                    String name = device.getName();
                    deviceInfo += (name != null ? name : "Unknown Device");
                } else {
                    deviceInfo += "Unknown Device";
                }
                deviceInfo += "\nAddress: " + device.getAddress();
                
                Toast.makeText(MainActivity.this, deviceInfo, Toast.LENGTH_LONG).show();
                Log.d(TAG, "User selected device: " + device.getAddress());
                Log.d(TAG, "User selected device Name: " + device.getName());
                
                stopBluetoothScan();
                dialog.dismiss();
                connectToDevice(device);
            } else {
                Toast.makeText(MainActivity.this, "Invalid device selection", Toast.LENGTH_SHORT).show();
                Log.e(TAG, "Invalid position: " + position + ", list size: " + availableDevicesList.size());
            }
        });

        dialog.show();
        startBluetoothScan(availableDevicesAdapter, progressScanning, tvScanning);
    }

    private void startBluetoothScan(ArrayAdapter<String> adapter, ProgressBar progressBar, TextView textView) {
        if (scanning) return;

        availableDevicesList.clear();
        adapter.clear();
        currentAdapter = adapter; // Store reference to current adapter
        
        progressBar.setVisibility(View.VISIBLE);
        textView.setVisibility(View.VISIBLE);

        handler.postDelayed(() -> {
            stopBluetoothScan();
            progressBar.setVisibility(View.GONE);
            textView.setVisibility(View.GONE);
            currentAdapter = null; // Clear reference
        }, SCAN_PERIOD);

        scanning = true;
        
        // Check all required permissions before scanning
        boolean hasBluetoothScanPermission = true;
        boolean hasLocationPermission = true;
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            hasBluetoothScanPermission = ActivityCompat.checkSelfPermission(this, Manifest.permission.BLUETOOTH_SCAN) == PackageManager.PERMISSION_GRANTED;
        }
        hasLocationPermission = ActivityCompat.checkSelfPermission(this, Manifest.permission.ACCESS_FINE_LOCATION) == PackageManager.PERMISSION_GRANTED;
        
        if (hasBluetoothScanPermission && hasLocationPermission && bluetoothLeScanner != null) {
            Toast.makeText(this, "Starting BLE scan...", Toast.LENGTH_SHORT).show();
            Log.d(TAG, "Starting BLE scan with filters and settings");
            
            // Setup BLE scan filters and settings for better performance
            List<ScanFilter> filters = new ArrayList<>();
            // Empty filter list means scan for all BLE devices
            
            ScanSettings settings = new ScanSettings.Builder()
                .setScanMode(ScanSettings.SCAN_MODE_LOW_LATENCY) // High power mode for better discovery
                .setReportDelay(0) // Report results immediately
                .setCallbackType(ScanSettings.CALLBACK_TYPE_ALL_MATCHES) // Report all matches
                .build();
                
            bluetoothLeScanner.startScan(filters, settings, scanCallback);
        } else {
            String missingPermissions = "";
            if (!hasBluetoothScanPermission) missingPermissions += "BLUETOOTH_SCAN ";
            if (!hasLocationPermission) missingPermissions += "LOCATION ";
            Toast.makeText(this, "Missing permissions: " + missingPermissions, Toast.LENGTH_LONG).show();
            scanning = false;
            progressBar.setVisibility(View.GONE);
            textView.setVisibility(View.GONE);
            currentAdapter = null;
        }
    }

    // Create a reusable ScanCallback
    private ScanCallback scanCallback = new ScanCallback() {
        @Override
        public void onScanResult(int callbackType, ScanResult result) {
            BluetoothDevice device = result.getDevice();
            if (!availableDevicesList.contains(device)) {
                Log.d(TAG, "Found device: " + device.getAddress());
                availableDevicesList.add(device);
                
                // Store device name in final variable for lambda - FIXED WITH SCANRECORD
                final String deviceName;
                
                // First try to get name from ScanRecord (more reliable for BLE)
                String advertisedName = null;
                if (result.getScanRecord() != null) {
                    advertisedName = result.getScanRecord().getDeviceName();
                }
                
                // Then try to get name from BluetoothDevice
                String bluetoothDeviceName = null;
                if (ActivityCompat.checkSelfPermission(MainActivity.this, Manifest.permission.BLUETOOTH_CONNECT) == PackageManager.PERMISSION_GRANTED) {
                    bluetoothDeviceName = device.getName();
                }
                
                // Priority: advertisedName > bluetoothDeviceName > Unknown Device
                if (advertisedName != null && !advertisedName.isEmpty()) {
                    deviceName = advertisedName; // Use advertised name (most reliable)
                } else if (bluetoothDeviceName != null && !bluetoothDeviceName.isEmpty()) {
                    deviceName = bluetoothDeviceName; // Use bluetooth device name
                } else {
                    deviceName = "Unknown Device"; // Fallback
                }
                
                // Store address in final variable for lambda
                final String deviceAddress = device.getAddress();
                
                // Get RSSI for signal strength
                final int rssi = result.getRssi();
                
                runOnUiThread(() -> {
                    if (currentAdapter != null) {
                        // Show device name, address and signal strength
                        currentAdapter.add(deviceName + "\n" + deviceAddress + " (RSSI: " + rssi + ")");
                        currentAdapter.notifyDataSetChanged();
                    }
                });
            }
        }
        
        @Override
        public void onScanFailed(int errorCode) {
            // Create the error message before the lambda - FIXED VERSION
            final String finalErrorMessage;
            
            switch (errorCode) {
                case ScanCallback.SCAN_FAILED_ALREADY_STARTED:
                    finalErrorMessage = "Scan failed with error: " + errorCode + " (Already started)";
                    break;
                case ScanCallback.SCAN_FAILED_APPLICATION_REGISTRATION_FAILED:
                    finalErrorMessage = "Scan failed with error: " + errorCode + " (App registration failed)";
                    break;
                case ScanCallback.SCAN_FAILED_FEATURE_UNSUPPORTED:
                    finalErrorMessage = "Scan failed with error: " + errorCode + " (Feature unsupported)";
                    break;
                case ScanCallback.SCAN_FAILED_INTERNAL_ERROR:
                    finalErrorMessage = "Scan failed with error: " + errorCode + " (Internal error)";
                    break;
                default:
                    finalErrorMessage = "Scan failed with error: " + errorCode;
                    break;
            }
            
            // Use the final variable in the lambda
            runOnUiThread(() -> {
                Toast.makeText(MainActivity.this, finalErrorMessage, Toast.LENGTH_LONG).show();
                scanning = false;
            });
        }
    };

    private void stopBluetoothScan() {
        if (scanning) {
            scanning = false;
            if (bluetoothLeScanner != null) {
                boolean hasPermission = true;
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                    hasPermission = ActivityCompat.checkSelfPermission(this, Manifest.permission.BLUETOOTH_SCAN) == PackageManager.PERMISSION_GRANTED;
                }
                if (hasPermission) {
                    try {
                        bluetoothLeScanner.stopScan(scanCallback);
                    } catch (Exception e) {
                        // Catch possible IllegalStateException
                        e.printStackTrace();
                    }
                }
            }
        }
    }

    private void connectToDevice(BluetoothDevice device) {
        Log.d(TAG, "connectToDevice called for: " + device.getAddress());
        
        // Double-check permissions before proceeding using your method
        if (!isPermissionsGot()) {
            Toast.makeText(this, "Permissions not granted! Please enable all permissions.", Toast.LENGTH_LONG).show();
            Log.e(TAG, "Permissions not granted");
            requestPermissions();
            return;
        }
        
        // Close existing connection if any
        if (bluetoothGatt != null) {
            Log.d("BLE", "Closing existing GATT connection");
            try {
                if (ActivityCompat.checkSelfPermission(this, Manifest.permission.BLUETOOTH_CONNECT) == PackageManager.PERMISSION_GRANTED) {
                    bluetoothGatt.disconnect();
                    bluetoothGatt.close();
                }
            } catch (Exception e) {
                Log.e("BLE", "Error closing existing connection: " + e.getMessage());
            }
            bluetoothGatt = null;
        }

        // Check device bond state for logging purposes
        int bondState = device.getBondState();
        Log.d("BLE", "Device bond state: " + bondState);
        
        // For SGN-168 and similar BLE devices, pairing is not required
        // Skip pairing and connect directly via GATT
        Toast.makeText(this, "Connecting to BLE device (no pairing required)...", Toast.LENGTH_SHORT).show();
        Log.d("BLE", "Connecting directly to BLE device without pairing");
        
        // Attempt GATT connection directly
        Toast.makeText(this, "Establishing GATT connection...", Toast.LENGTH_SHORT).show();
        Log.d("BLE", "Starting GATT connection...");
        
        try {
            bluetoothGatt = device.connectGatt(this, false, gattCallback);
            if (bluetoothGatt != null) {
                Log.d("BLE", "GATT connection initiated successfully");
            } else {
                Log.e("BLE", "Failed to initiate GATT connection - returned null");
                Toast.makeText(this, "Failed to start GATT connection", Toast.LENGTH_LONG).show();
            }
        } catch (Exception e) {
            Log.e("BLE", "Exception during GATT connection: " + e.getMessage());
            Toast.makeText(this, "Error connecting: " + e.getMessage(), Toast.LENGTH_LONG).show();
        }
    }

    private final BluetoothGattCallback gattCallback = new BluetoothGattCallback() {
        @Override
        public void onConnectionStateChange(BluetoothGatt gatt, int status, int newState) {
            if (newState == BluetoothProfile.STATE_CONNECTED) {
                Log.d(TAG, "onConnectionStateChange -  STATE_CONNECTED!!!!!!!!!!!!!");

                // Save both device and gatt references
                connectedDevice = gatt.getDevice();
                bluetoothGatt = gatt; // Important: Save the gatt reference for sending commands

                Log.w(TAG, "✅ Bluetooth GATT connection established");
                Log.w(TAG, "   Device: " + gatt.getDevice().getName());
                Log.w(TAG, "   Address: " + gatt.getDevice().getAddress());
                Log.w(TAG, "   Status: " + status);

                runOnUiThread(() -> {
                    Toast.makeText(MainActivity.this, "BLE Device Connected Successfully!", Toast.LENGTH_SHORT).show();
                    btnUpdateFirmware.setEnabled(true);

                    // For BLE devices that don't pair, add to connected devices list
                    addToConnectedDevicesList(gatt.getDevice());

                    Log.d("BLE", "BLE device connected successfully");
                });

                // Discover services after connection
                if (ActivityCompat.checkSelfPermission(MainActivity.this, Manifest.permission.BLUETOOTH_CONNECT) == PackageManager.PERMISSION_GRANTED) {
                    Log.d(TAG, "Starting service discovery...");
                    gatt.discoverServices();
                } else {
                    Log.e(TAG, "❌ Missing BLUETOOTH_CONNECT permission for service discovery");
                }
            } else if (newState == BluetoothProfile.STATE_DISCONNECTED) {
                Log.d(TAG, "STATE_DISCONNECTED!!!!!!!!!!!!!");

                // Clear references
                connectedDevice = null;
                bluetoothGatt = null;

                Log.w(TAG, "❌ Bluetooth GATT disconnected");
                Log.w(TAG, "   Status: " + status);

                runOnUiThread(() -> {
                    Toast.makeText(MainActivity.this, "BLE Device Disconnected", Toast.LENGTH_SHORT).show();
                    btnUpdateFirmware.setEnabled(false);

                    // Update connected devices list
                    removeFromConnectedDevicesList(gatt.getDevice());

                    Log.d("BLE", "BLE device disconnected");
                });
            }
        }
        
        @Override
        public void onServicesDiscovered(BluetoothGatt gatt, int status) {
            Log.w(TAG, "=== GATT SERVICES DISCOVERY ===");
            Log.w(TAG, "Status: " + status + " (SUCCESS=" + BluetoothGatt.GATT_SUCCESS + ")");

            if (status == BluetoothGatt.GATT_SUCCESS) {
                Log.w(TAG, "✅ Services discovered successfully");

                // Log available services for debugging
                if (gatt.getServices() != null) {
                    Log.w(TAG, "Available services count: " + gatt.getServices().size());
                    for (BluetoothGattService service : gatt.getServices()) {
                        Log.d(TAG, "  Service UUID: " + service.getUuid());
                    }
                }

                // Check if our required service exists
                BluetoothGattService targetService = gatt.getService(SGN168GattAttributes.GATT_SERVICE);
                if (targetService != null) {
                    Log.w(TAG, "✅ Target service found: " + SGN168GattAttributes.GATT_SERVICE);

                    // Check characteristics
                    BluetoothGattCharacteristic writeChar = targetService.getCharacteristic(SGN168GattAttributes.WRITE_CHARACTERISTIC);
                    BluetoothGattCharacteristic readChar = targetService.getCharacteristic(SGN168GattAttributes.READ_CHARACTERISTIC);

                    Log.w(TAG, "Write characteristic: " + (writeChar != null ? "✅ Found" : "❌ Not found"));
                    Log.w(TAG, "Read characteristic: " + (readChar != null ? "✅ Found" : "❌ Not found"));
                } else {
                    Log.e(TAG, "❌ Target service NOT found: " + SGN168GattAttributes.GATT_SERVICE);
                }

                runOnUiThread(() -> {
                    Toast.makeText(MainActivity.this, "Services discovered", Toast.LENGTH_SHORT).show();
                });

                // Enable notifications for receiving responses
                enableBluetoothNotifications(gatt);
            } else {
                Log.e(TAG, "❌ Service discovery failed with status: " + status);
                runOnUiThread(() -> {
                    Toast.makeText(MainActivity.this, "Service discovery failed", Toast.LENGTH_LONG).show();
                });
            }
        }

        @Override
        public void onCharacteristicChanged(BluetoothGatt gatt, BluetoothGattCharacteristic characteristic) {
            // This method is called when the device sends data back to us
            byte[] values = characteristic.getValue();

            if (values != null && values.length > 0) {
                // Print bluetooth response in the same format as LeDataService
                String responseHex = AFPCommand.bytesToHex(values, values.length);
                Log.w(TAG, "Res : " + responseHex);

                // Additional analysis for debugging
                if (values.length > 7 && values[0] == (byte) 0xFF) {
                    // Parse opcode like in LeDataService
                    String opcode = String.format("%02X%02X", values[7] & 0xFF, values[6] & 0xFF);
                    Log.w(TAG, "Response OpCode: " + opcode);

                    // Check if it's a Command0001 response (8001) - Firmware Version
                    if ("8001".equals(opcode)) {
                        Log.w(TAG, "Command0001 Response Received - Firmware Version");
                        String firmwareVersion = Command0001.GetFirmwareVersionToStr(values);
                        Log.w(TAG, "Firmware Version: " + firmwareVersion);

                        // Show firmware version to user
                        runOnUiThread(() -> {
                            Toast.makeText(MainActivity.this,
                                "Device Firmware: " + firmwareVersion,
                                Toast.LENGTH_LONG).show();
                        });

                    // Check if it's a Command0002 response (8002) - Memory Operation
                    } else if ("8002".equals(opcode)) {
                        Log.w(TAG, "Command0002 Response Received");
                        if (values.length > 8) {
                            String memoryOperation = String.format("%02X", values[8] & 0xFF);
                            if ("03".equals(memoryOperation)) {
                                Log.w(TAG, "Memory Write Response");
                            } else if ("02".equals(memoryOperation)) {
                                Log.w(TAG, "Memory Read Response");
                            }
                        }
                    } else {
                        Log.w(TAG, "Unknown OpCode response: " + opcode);
                    }
                } else {
                    Log.w(TAG, "Invalid response format or length: " + values.length);
                }
            } else {
                Log.w(TAG, "Received empty or null response");
            }
        }
    };

    /**
     * Enable Bluetooth notifications to receive responses from device (based on LeDataService)
     */
    private void enableBluetoothNotifications(BluetoothGatt gatt) {
        Log.w(TAG, "=== ENABLING BLUETOOTH NOTIFICATIONS ===");

        try {
            if (ActivityCompat.checkSelfPermission(this, Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED) {
                Log.e(TAG, "❌ Bluetooth permission not granted");
                return;
            }

            // Check device name (like in LeDataService)
            String deviceName = gatt.getDevice().getName();
            Log.w(TAG, "Device name: " + deviceName);

            // Get the service
            BluetoothGattService service = gatt.getService(SGN168GattAttributes.GATT_SERVICE);
            if (service == null) {
                Log.e(TAG, "❌ GATT service not found: " + SGN168GattAttributes.GATT_SERVICE);

                // List all available services for debugging
                Log.w(TAG, "Available services:");
                for (BluetoothGattService availableService : gatt.getServices()) {
                    Log.w(TAG, "  Service: " + availableService.getUuid());
                }
                return;
            }
            Log.w(TAG, "✅ GATT service found: " + SGN168GattAttributes.GATT_SERVICE);

            // Get the read characteristic (following LeDataService pattern)
            BluetoothGattCharacteristic characteristic = service.getCharacteristic(SGN168GattAttributes.READ_CHARACTERISTIC);
            if (characteristic == null) {
                Log.e(TAG, "❌ Read characteristic not found: " + SGN168GattAttributes.READ_CHARACTERISTIC);

                // List all available characteristics for debugging
                Log.w(TAG, "Available characteristics:");
                for (BluetoothGattCharacteristic availableChar : service.getCharacteristics()) {
                    Log.w(TAG, "  Characteristic: " + availableChar.getUuid());
                }
                return;
            }
            Log.w(TAG, "✅ Read characteristic found: " + SGN168GattAttributes.READ_CHARACTERISTIC);

            // Enable notifications following LeDataService pattern (lines 221-226)
            for (int k = 0; k < characteristic.getDescriptors().size(); k++) {
                BluetoothGattDescriptor descriptor = characteristic.getDescriptors().get(k);
                descriptor.setValue(BluetoothGattDescriptor.ENABLE_NOTIFICATION_VALUE);
                boolean descriptorWritten = gatt.writeDescriptor(descriptor);
                Log.w(TAG, "Descriptor " + k + " written: " + descriptorWritten + " (UUID: " + descriptor.getUuid() + ")");
            }

            // Set characteristic notification
            boolean notificationSet = gatt.setCharacteristicNotification(characteristic, true);
            Log.w(TAG, "Characteristic notification set: " + notificationSet);

            if (notificationSet && characteristic.getDescriptors().size() > 0) {
                Log.w(TAG, "✅ Bluetooth notifications enabled successfully");
            } else {
                Log.e(TAG, "❌ Failed to enable notifications properly");
            }

        } catch (Exception e) {
            Log.e(TAG, "❌ Exception enabling Bluetooth notifications: " + e.getMessage(), e);
        }
    }

    private void scanHexFiles() {
        Log.d("HexScan", "=== Starting Hex file scan ===");
        hexFilesList.clear();
        hexFilesAdapter.clear();

        // Get Downloads directory path
        File downloadsDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS);
        Log.d("HexScan", "Downloads directory path: " + downloadsDir.getAbsolutePath());
        
        if (!downloadsDir.exists()) {
            Log.e("HexScan", "Downloads directory does not exist");
            Toast.makeText(this, "Downloads folder not found", Toast.LENGTH_LONG).show();
            hexFilesAdapter.add("Downloads folder not found\nPath: " + downloadsDir.getAbsolutePath());
            hexFilesAdapter.notifyDataSetChanged();
            return;
        }
        
        if (!downloadsDir.isDirectory()) {
            Log.e("HexScan", "Downloads path is not a directory");
            Toast.makeText(this, "Downloads path is not a directory", Toast.LENGTH_LONG).show();
            return;
        }
        
        // For Android 11+, use MediaStore API to access Downloads
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            scanHexFilesUsingMediaStore();
        } else {
            scanHexFilesUsingFileSystem();
        }
    }
    
    private void scanHexFilesUsingMediaStore() {
        Log.d("HexScan", "Using MediaStore API for Android 10+");
        
        ContentResolver contentResolver = getContentResolver();
        Uri downloadsUri = MediaStore.Downloads.EXTERNAL_CONTENT_URI;
        
        // First, try to query all files to see what's available
        String[] allProjection = {MediaStore.Downloads.DISPLAY_NAME};
        try (Cursor allCursor = contentResolver.query(downloadsUri, allProjection, null, null, null)) {
            if (allCursor != null) {
                Log.d("HexScan", "Total files in Downloads via MediaStore: " + allCursor.getCount());
                while (allCursor.moveToNext()) {
                    String fileName = allCursor.getString(0);
                    Log.d("HexScan", "Found file: " + fileName);
                }
            }
        } catch (Exception e) {
            Log.e("HexScan", "Error querying all files: " + e.getMessage());
        }
        
        // Query for .hex files with multiple approaches
        String[] projection = {
            MediaStore.Downloads._ID,
            MediaStore.Downloads.DISPLAY_NAME,
            MediaStore.Downloads.SIZE,
            MediaStore.Downloads.DATA
        };
        
        // Try different selection criteria
        String[] selections = {
            MediaStore.Downloads.DISPLAY_NAME + " LIKE '%.hex'",
            MediaStore.Downloads.DISPLAY_NAME + " LIKE '%.HEX'",
            "LOWER(" + MediaStore.Downloads.DISPLAY_NAME + ") LIKE '%.hex'"
        };
        
        boolean foundFiles = false;
        
        for (String selection : selections) {
            Log.d("HexScan", "Trying selection: " + selection);
            
            try (Cursor cursor = contentResolver.query(downloadsUri, projection, selection, null, MediaStore.Downloads.DISPLAY_NAME + " ASC")) {
                if (cursor != null && cursor.getCount() > 0) {
                    Log.d("HexScan", "Found " + cursor.getCount() + " .hex files with selection: " + selection);
                    foundFiles = true;
                    
                    while (cursor.moveToNext()) {
                        String fileName = cursor.getString(cursor.getColumnIndexOrThrow(MediaStore.Downloads.DISPLAY_NAME));
                        long fileSize = cursor.getLong(cursor.getColumnIndexOrThrow(MediaStore.Downloads.SIZE));
                        String filePath = cursor.getString(cursor.getColumnIndexOrThrow(MediaStore.Downloads.DATA));
                        
                        String fileSizeStr = formatFileSize(fileSize);
                        
                        if (!hexFilesList.contains(fileName)) {
                            hexFilesList.add(fileName);
                            hexFilesAdapter.add(fileName + "\nSize: " + fileSizeStr);
                            
                            Log.d("HexScan", "Added hex file: " + fileName + " (" + fileSizeStr + ")");
                            Log.d("HexScan", "File path: " + filePath);
                        }
                    }
                    break; // Found files, no need to try other selections
                }
            } catch (Exception e) {
                Log.e("HexScan", "Error with selection '" + selection + "': " + e.getMessage());
            }
        }
        
        if (!foundFiles) {
            Log.d("HexScan", "No .hex files found via MediaStore, trying fallback to file system");
            // Fallback to file system approach
            scanHexFilesUsingFileSystem();
            return;
        } else {
            Toast.makeText(this, "Found " + hexFilesList.size() + " hex file(s)", Toast.LENGTH_SHORT).show();
        }
        
        hexFilesAdapter.notifyDataSetChanged();
        Log.d("HexScan", "=== Finished MediaStore Hex file scan ===");
    }
    
    private void scanHexFilesUsingFileSystem() {
        Log.d("HexScan", "Using File System API for older Android versions");
        
        // Get Downloads directory path
        File downloadsDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS);
        Log.d("HexScan", "Downloads directory path: " + downloadsDir.getAbsolutePath());
        
        if (!downloadsDir.exists()) {
            Log.e("HexScan", "Downloads directory does not exist");
            Toast.makeText(this, "Downloads folder not found", Toast.LENGTH_LONG).show();
            hexFilesAdapter.add("Downloads folder not found\nPath: " + downloadsDir.getAbsolutePath());
            hexFilesAdapter.notifyDataSetChanged();
            return;
        }
        
        Log.d("HexScan", "Downloads directory exists, scanning for .hex files...");
        
        // Scan for .hex files (handles filenames with spaces)
        File[] allFiles = downloadsDir.listFiles();
        if (allFiles == null) {
            Log.e("HexScan", "Cannot list files in Downloads directory (permission issue?)");
            Toast.makeText(this, "Cannot access Downloads folder. Check storage permissions.", Toast.LENGTH_LONG).show();
            hexFilesAdapter.add("Cannot access Downloads folder\nCheck storage permissions");
            hexFilesAdapter.notifyDataSetChanged();
            return;
        }
        
        Log.d("HexScan", "Found " + allFiles.length + " total files in Downloads");
        
        // Filter .hex files (handles filenames with spaces)
        File[] hexFiles = downloadsDir.listFiles((dir, name) -> name.toLowerCase().endsWith(".hex"));
        
        if (hexFiles != null && hexFiles.length > 0) {
            Log.d("HexScan", "Found " + hexFiles.length + " .hex files");
            
            for (File file : hexFiles) {
                String fileName = file.getName();
                long fileSize = file.length();
                String fileSizeStr = formatFileSize(fileSize);
                
                hexFilesList.add(fileName);
                hexFilesAdapter.add(fileName + "\nSize: " + fileSizeStr);
                Log.d("HexScan", "Added hex file: " + fileName + " (" + fileSizeStr + ")");
            }
            
            Toast.makeText(this, "Found " + hexFiles.length + " hex file(s)", Toast.LENGTH_SHORT).show();
        } else {
            Log.d("HexScan", "No .hex files found in Downloads directory");
            Toast.makeText(this, "No .hex files found in Downloads folder", Toast.LENGTH_SHORT).show();
            hexFilesAdapter.add("No .hex files found\nPlace .hex files in Downloads folder");
        }

        hexFilesAdapter.notifyDataSetChanged();
        Log.d("HexScan", "=== Finished File System Hex file scan ===");
    }
    
    private String formatFileSize(long bytes) {
        if (bytes < 1024) return bytes + " B";
        int exp = (int) (Math.log(bytes) / Math.log(1024));
        String pre = "KMGTPE".charAt(exp-1) + "";
        return String.format("%.1f %sB", bytes / Math.pow(1024, exp), pre);
    }

    /**
     * Parse hex file immediately after SAF selection to verify data (in background thread)
     */
    private void parseHexFileImmediately(String hexFilePath, String fileName) {
        Log.w(TAG, "=== IMMEDIATE HEX PARSING START ===");
        Log.w(TAG, "File: " + fileName);
        Log.w(TAG, "Path: " + hexFilePath);

        // Show loading message
        Toast.makeText(this, "Parsing hex file in background...", Toast.LENGTH_SHORT).show();

        // Execute in background thread
        new Thread(() -> {
            try {
                // Create AFPUpdateTask and parse hex file
                AFPUpdateTask updateTask = new AFPUpdateTask(this);
                boolean parseSuccess = updateTask.parseHexFile(hexFilePath);

                // Switch back to UI thread for UI updates
                runOnUiThread(() -> {
                    if (parseSuccess) {
                        // Get parsed data for verification
                        int totalHexLines = AFPUpdateTask.getUpdateHexStrings().size();
                        int totalDataBlocks = AFPUpdateTask.getUpdateSubDatas().size();

                        Log.w(TAG, "=== PARSING RESULTS ===");
                        Log.w(TAG, "Parse Success: " + parseSuccess);
                        Log.w(TAG, "Total Hex Lines: " + totalHexLines);
                        Log.w(TAG, "Total Data Blocks: " + totalDataBlocks);
                        Log.w(TAG, "=== IMMEDIATE HEX PARSING COMPLETE ===");

                        // Show result to user
                        Toast.makeText(this,
                            "Hex file parsed successfully!\n" +
                            "Lines: " + totalHexLines + "\n" +
                            "Data blocks: " + totalDataBlocks + "\n" +
                            "Check txt file in Downloads folder",
                            Toast.LENGTH_LONG).show();

                    } else {
                        Log.e(TAG, "=== PARSING FAILED ===");
                        Toast.makeText(this, "Failed to parse hex file!", Toast.LENGTH_LONG).show();
                    }
                });

            } catch (Exception e) {
                Log.e(TAG, "Error in immediate hex parsing: " + e.getMessage(), e);
                runOnUiThread(() -> {
                    Toast.makeText(this, "Error parsing hex file: " + e.getMessage(), Toast.LENGTH_LONG).show();
                });
            }
        }).start();
    }

    /**
     * Start firmware update process
     */
    private void startFirmwareUpdate() {
        Log.d(TAG, "=== Starting Firmware Update Process ===");

        // Validate prerequisites
        if (connectedDevice == null) {
            Toast.makeText(this, "Please connect to a device first", Toast.LENGTH_LONG).show();
            Log.e(TAG, "No device connected");
            return;
        }

        if (selectedHexFilePath == null || selectedHexFilePath.isEmpty()) {
            Toast.makeText(this, "Please select a hex file first", Toast.LENGTH_LONG).show();
            Log.e(TAG, "No hex file selected");
            return;
        }

        Log.d(TAG, "Connected device: " + connectedDevice.getAddress());
        Log.d(TAG, "Selected hex file: " + selectedHexFilePath);

        // Create and start firmware update manager
        FirmwareUpdateManager updateManager = new FirmwareUpdateManager(this, handler);
        updateManager.setConnectedDevice(connectedDevice, bluetoothGatt);
        updateManager.setSelectedHexFile(selectedHexFilePath);
        updateManager.startFirmwareUpdate();
    }



    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        
        if (requestCode == REQUEST_BLUETOOTH_PERMISSIONS || requestCode == PERMISSION_REQUEST_CODE) {
            StringBuilder result = new StringBuilder("Permission results:\n");
            boolean allGranted = true;
            
            for (int i = 0; i < permissions.length; i++) {
                boolean granted = grantResults[i] == PackageManager.PERMISSION_GRANTED;
                result.append(permissions[i]).append(": ").append(granted ? "GRANTED" : "DENIED").append("\n");
                if (!granted) {
                    allGranted = false;
                }
                Log.d(TAG, permissions[i] + ": " + (granted ? "GRANTED" : "DENIED"));
            }
            
            Toast.makeText(this, result.toString(), Toast.LENGTH_LONG).show();
            
            if (allGranted) {
                loadPairedDevices();
                Toast.makeText(this, "All permissions granted! You can now scan for devices.", Toast.LENGTH_SHORT).show();
            } else {
                Toast.makeText(this, "Some permissions denied. Please grant all permissions in Settings.", Toast.LENGTH_LONG).show();
                // Show which specific permissions are missing
                checkAndShowMissingPermissions();
            }
        }
    }
    
    private void checkAndShowMissingPermissions() {
        StringBuilder missing = new StringBuilder("Missing permissions:\n");
        boolean hasMissing = false;
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            if (ContextCompat.checkSelfPermission(this, Manifest.permission.BLUETOOTH_SCAN) != PackageManager.PERMISSION_GRANTED) {
                missing.append("- BLUETOOTH_SCAN\n");
                hasMissing = true;
            }
            if (ContextCompat.checkSelfPermission(this, Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED) {
                missing.append("- BLUETOOTH_CONNECT\n");
                hasMissing = true;
            }
            if (ContextCompat.checkSelfPermission(this, Manifest.permission.BLUETOOTH_ADVERTISE) != PackageManager.PERMISSION_GRANTED) {
                missing.append("- BLUETOOTH_ADVERTISE\n");
                hasMissing = true;
            }
        }
        
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
            missing.append("- ACCESS_FINE_LOCATION\n");
            hasMissing = true;
        }
        
        if (hasMissing) {
            missing.append("\nPlease go to Settings > Apps > PigeonRingFwUpdateTool > Permissions and enable these permissions.");
            Toast.makeText(this, missing.toString(), Toast.LENGTH_LONG).show();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (bluetoothGatt != null) {
            if (ActivityCompat.checkSelfPermission(this, Manifest.permission.BLUETOOTH_CONNECT) == PackageManager.PERMISSION_GRANTED) {
                bluetoothGatt.disconnect();
                bluetoothGatt.close();
            }
        }
        stopBluetoothScan();
        
        // Unregister broadcast receiver
        try {
            unregisterReceiver(pairingReceiver);
        } catch (IllegalArgumentException e) {
            // Receiver was not registered
        }
    }
    
    private void debugBluetoothState() {
        StringBuilder debug = new StringBuilder("Debug Info:\n");
        
        // Check Bluetooth adapter state
        debug.append("BT Adapter: ").append(bluetoothAdapter != null ? "Available" : "NULL").append("\n");
        debug.append("BT Enabled: ").append(bluetoothAdapter != null && bluetoothAdapter.isEnabled()).append("\n");
        debug.append("BLE Scanner: ").append(bluetoothLeScanner != null ? "Available" : "NULL").append("\n");
        
        // Check permissions
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            debug.append("BLUETOOTH_SCAN: ").append(
                ActivityCompat.checkSelfPermission(this, Manifest.permission.BLUETOOTH_SCAN) == PackageManager.PERMISSION_GRANTED
            ).append("\n");
            debug.append("BLUETOOTH_CONNECT: ").append(
                ActivityCompat.checkSelfPermission(this, Manifest.permission.BLUETOOTH_CONNECT) == PackageManager.PERMISSION_GRANTED
            ).append("\n");
            debug.append("BLUETOOTH_ADVERTISE: ").append(
                ActivityCompat.checkSelfPermission(this, Manifest.permission.BLUETOOTH_ADVERTISE) == PackageManager.PERMISSION_GRANTED
            ).append("\n");
        }
        debug.append("LOCATION: ").append(
            ActivityCompat.checkSelfPermission(this, Manifest.permission.ACCESS_FINE_LOCATION) == PackageManager.PERMISSION_GRANTED
        ).append("\n");
        
        debug.append("Android Version: ").append(Build.VERSION.SDK_INT);
        
        Toast.makeText(this, debug.toString(), Toast.LENGTH_LONG).show();
        Log.d(TAG, debug.toString());
        
        // Also check if we need to request permissions again
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            if (ActivityCompat.checkSelfPermission(this, Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED) {
                Toast.makeText(this, "BLUETOOTH_CONNECT permission missing! Requesting again...", Toast.LENGTH_LONG).show();
                checkPermissions(); // Re-request permissions
            }
        }
    }
    
    /**
     * Check Permission - Based on your provided code
     */
    public boolean isPermissionsGot() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            return checkSelfPermission(BLUETOOTH_CONNECT) == PackageManager.PERMISSION_GRANTED
                    && checkSelfPermission(BLUETOOTH_SCAN) == PackageManager.PERMISSION_GRANTED
                    && checkSelfPermission(ACCESS_COARSE_LOCATION) == PackageManager.PERMISSION_GRANTED
                    && checkSelfPermission(ACCESS_FINE_LOCATION) == PackageManager.PERMISSION_GRANTED;
        } else {
            return checkSelfPermission(ACCESS_COARSE_LOCATION) == PackageManager.PERMISSION_GRANTED &&
                    checkSelfPermission(ACCESS_FINE_LOCATION) == PackageManager.PERMISSION_GRANTED &&
                    checkSelfPermission(READ_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED &&
                    checkSelfPermission(WRITE_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED;
        }
    }

    /**
     * request Permissions - Based on your provided code
     */
    public void requestPermissions() {
        ActivityCompat.requestPermissions(this, new String[]{
                ACCESS_COARSE_LOCATION,
                ACCESS_FINE_LOCATION,
                BLUETOOTH_CONNECT,
                BLUETOOTH_SCAN,
                READ_EXTERNAL_STORAGE,
                WRITE_EXTERNAL_STORAGE
        }, PERMISSION_REQUEST_CODE);
    }
    
    private void forcePermissionCheck() {
        // Use your permission checking method
        if (!isPermissionsGot()) {
            Toast.makeText(this, "Critical permissions missing! Requesting now...", Toast.LENGTH_LONG).show();
            Log.d(TAG, "Permissions not granted, requesting...");
            requestPermissions();
        } else {
            Toast.makeText(this, "All permissions granted!", Toast.LENGTH_SHORT).show();
            Log.d(TAG, "All permissions granted");
            loadPairedDevices();
        }
    }
}