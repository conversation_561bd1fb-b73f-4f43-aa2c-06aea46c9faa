<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="20dp">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Firmware Update"
        android:textSize="20sp"
        android:textStyle="bold"
        android:layout_marginBottom="16dp"
        android:layout_gravity="center" />

    <TextView
        android:id="@+id/tv_update_status"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Initializing..."
        android:textSize="16sp"
        android:layout_marginBottom="16dp"
        android:padding="8dp"
        android:background="#F0F0F0" />

    <TextView
        android:id="@+id/tv_send_command"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Send: -"
        android:textSize="14sp"
        android:layout_marginBottom="8dp"
        android:padding="8dp"
        android:background="#E8F5E8"
        android:textColor="#2E7D32" />

    <TextView
        android:id="@+id/tv_receive_command"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Receive: -"
        android:textSize="14sp"
        android:layout_marginBottom="16dp"
        android:padding="8dp"
        android:background="#E3F2FD"
        android:textColor="#1976D2" />

    <ProgressBar
        android:id="@+id/progress_update"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        style="?android:attr/progressBarStyleHorizontal"
        android:indeterminate="true" />

    <Button
        android:id="@+id/btn_cancel_update"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Cancel"
        android:layout_gravity="center" />

</LinearLayout>