package com.android.pigeonringfwupdatetool;

import android.content.Context;
import android.net.Uri;
import android.os.Environment;
import android.util.Log;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileWriter;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;

/**
 * AFP Update Task - Hex File Parser
 * Based on the original AFPUpdateTask.java with BANK switching logic
 */
public class AFPUpdateTask {
    
    private static final String TAG = "AFPUpdateTask";
    
    // Hex file data
    public static List<String> updateHexStrings = new ArrayList<>();
    public static List<UpdateSubData> updateSubDatas = new ArrayList<>();
    
    private Context context;
    
    public AFPUpdateTask(Context context) {
        this.context = context;
    }
    
    /**
     * Parse hex file from URI (for SAF selected files)
     */
    public boolean parseHexFile(String hexFilePath) {
        Log.d(TAG, "=== Starting Hex File Parsing ===");
        Log.d(TAG, "Hex file path: " + hexFilePath);
        
        // Clear previous data
        updateHexStrings.clear();
        updateSubDatas.clear();
        
        try {
            // Check if it's a URI (from file picker) or file path
            if (hexFilePath.startsWith("content://")) {
                return parseHexFileFromUri(Uri.parse(hexFilePath));
            } else {
                return parseHexFileFromPath(hexFilePath);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error parsing hex file: " + e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * Parse hex file from URI (Storage Access Framework)
     */
    private boolean parseHexFileFromUri(Uri uri) {
        try {
            InputStream inputStream = context.getContentResolver().openInputStream(uri);
            if (inputStream == null) {
                Log.e(TAG, "Cannot open input stream from URI");
                return false;
            }
            
            BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream));
            return parseHexContent(reader);
            
        } catch (Exception e) {
            Log.e(TAG, "Error reading hex file from URI: " + e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * Parse hex file from file path (traditional method)
     */
    private boolean parseHexFileFromPath(String filePath) {
        try {
            java.io.File file = new java.io.File(filePath);
            if (!file.exists()) {
                Log.e(TAG, "Hex file does not exist: " + filePath);
                return false;
            }
            
            java.io.FileInputStream fis = new java.io.FileInputStream(file);
            BufferedReader reader = new BufferedReader(new InputStreamReader(fis));
            return parseHexContent(reader);
            
        } catch (Exception e) {
            Log.e(TAG, "Error reading hex file from path: " + e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * Parse hex content from BufferedReader
     */
    private boolean parseHexContent(BufferedReader reader) {
        try {
            String line;
            int lineNumber = 0;
            int totalLines = 0;
            long totalBytes = 0;
            long currentAddress = 0;
            long extendedAddress = 0; // For extended linear address records

            // Create txt file for saving hex data
            String timestamp = new SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(new Date());
            String txtFileName = "hex_parsed_" + timestamp + ".txt";
            File downloadsDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS);
            File txtFile = new File(downloadsDir, txtFileName);
            FileWriter txtWriter = null;

            try {
                txtWriter = new FileWriter(txtFile);
                txtWriter.write("=== HEX FILE PARSING RESULTS ===\n");
                txtWriter.write("Timestamp: " + new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(new Date()) + "\n");
                txtWriter.write("=== HEX STRINGS ===\n");
            } catch (Exception e) {
                Log.e(TAG, "Failed to create txt file: " + e.getMessage());
                txtWriter = null;
            }

            Log.d(TAG, "=== HEX FILE CONTENT START ===");
            Log.d(TAG, "=== HEX STRINGS ===");

            while ((line = reader.readLine()) != null) {
                lineNumber++;
                line = line.trim();

                if (line.isEmpty()) {
                    continue;
                }

                totalLines++;
                totalBytes += line.length();

                // Store original hex string first
                updateHexStrings.add(line);

                // Print each hex line raw data with Log.w for verification
                String rawLineLog = String.format("Raw Line %04d: %s", lineNumber, line);
//                Log.w(TAG, rawLineLog);

                // Write to txt file if available
                if (txtWriter != null) {
                    try {
                        txtWriter.write(rawLineLog + "\n");
                    } catch (Exception e) {
                        Log.e(TAG, "Error writing to txt file: " + e.getMessage());
                    }
                }

                // Parse Intel Hex format
                if (!line.startsWith(":")) {
                    Log.w(TAG, String.format("Line %04d: Invalid hex format (missing ':')", lineNumber));
                    continue;
                }

                try {
                    // Parse hex record
                    HexRecord record = parseHexRecord(line);
                    if (record == null) {
                        continue;
                    }

                    // Log detailed record information
                    String recordTypeStr = getRecordTypeString(record.recordType);
//                    Log.d(TAG, String.format("        -> Bytes: %02X, Addr: %04X, Type: %02X (%s), Checksum: %02X",
//                        record.byteCount, record.address, record.recordType, recordTypeStr, record.checksum));

                    switch (record.recordType) {
                        case 0x00: // Data record
                            // Log data content for data records
                            if (record.byteCount > 0) {
                                StringBuilder dataHex = new StringBuilder();
                                for (int i = 0; i < record.byteCount; i++) {
                                    dataHex.append(String.format("%02X ", record.data[i] & 0xFF));
                                }
//                                Log.d(TAG, String.format("        -> Data: %s", dataHex.toString().trim()));
                            }
                            processDataRecord(record, extendedAddress);
                            break;

                        case 0x01: // End of file record
//                            Log.d(TAG, "        -> End of file record found");
                            break;

                        case 0x04: // Extended Linear Address Record
                            extendedAddress = ((long) record.data[0] << 24) | ((long) record.data[1] << 16);
//                            Log.d(TAG, String.format("        -> Extended address set to: 0x%08X", extendedAddress));
                            break;

                        case 0x05: // Start Linear Address Record
//                            Log.d(TAG, "        -> Start address record (ignored)");
                            break;

                        default:
//                            Log.w(TAG, String.format("        -> Unknown record type: 0x%02X", record.recordType));
                            break;
                    }

                } catch (Exception e) {
                    Log.e(TAG, String.format("Line %04d: Error parsing hex record: %s", lineNumber, e.getMessage()));
                }
            }

            reader.close();

            // Write summary to txt file and close it
            if (txtWriter != null) {
                try {
                    txtWriter.write("\n=== PARSING SUMMARY ===\n");
                    txtWriter.write(String.format("Total lines processed: %d\n", totalLines));
                    txtWriter.write(String.format("Total characters: %d\n", totalBytes));
                    txtWriter.write(String.format("Total hex strings stored: %d\n", updateHexStrings.size()));
                    txtWriter.write(String.format("Total data blocks created: %d\n", updateSubDatas.size()));
                    txtWriter.write("=== HEX FILE PARSING COMPLETE ===\n");
                    txtWriter.close();
                    Log.w(TAG, "Hex data saved to: " + txtFile.getAbsolutePath());
                } catch (Exception e) {
                    Log.e(TAG, "Error closing txt file: " + e.getMessage());
                }
            }

            Log.d(TAG, "=== HEX FILE CONTENT END ===");
            Log.d(TAG, String.format("Total lines processed: %d", totalLines));
            Log.d(TAG, String.format("Total characters: %d", totalBytes));
            Log.d(TAG, String.format("Total hex strings stored: %d", updateHexStrings.size()));
            Log.d(TAG, String.format("Total data blocks created: %d", updateSubDatas.size()));
            Log.d(TAG, "=== HEX FILE PARSING COMPLETE ===");
            
            // Apply BANK switching logic
            applyBankSwitchingLogic();
            
            return true;
            
        } catch (Exception e) {
            Log.e(TAG, "Error parsing hex content: " + e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * Parse a single hex record line
     */
    private HexRecord parseHexRecord(String line) {
        if (line.length() < 11) { // Minimum hex record length
            Log.w(TAG, "Hex line too short: " + line);
            return null;
        }
        
        try {
            // Remove ':' prefix
            String hexData = line.substring(1);
            
            // Parse fields
            int byteCount = Integer.parseInt(hexData.substring(0, 2), 16);
            int address = Integer.parseInt(hexData.substring(2, 6), 16);
            int recordType = Integer.parseInt(hexData.substring(6, 8), 16);
            
            // Extract data bytes
            byte[] data = new byte[byteCount];
            for (int i = 0; i < byteCount; i++) {
                int dataIndex = 8 + (i * 2);
                data[i] = (byte) Integer.parseInt(hexData.substring(dataIndex, dataIndex + 2), 16);
            }
            
            // Extract checksum
            int checksumIndex = 8 + (byteCount * 2);
            int checksum = Integer.parseInt(hexData.substring(checksumIndex, checksumIndex + 2), 16);
            
            return new HexRecord(byteCount, address, recordType, data, checksum);
            
        } catch (Exception e) {
            Log.e(TAG, "Error parsing hex record: " + line, e);
            return null;
        }
    }
    
    /**
     * Process data record and create UpdateSubData
     */
    private void processDataRecord(HexRecord record, long extendedAddress) {
        long fullAddress = extendedAddress + record.address;
        
//        Log.d(TAG, "Data record - Address: 0x" + Long.toHexString(fullAddress) +
//               ", Length: " + record.byteCount + " bytes");
        
        // Create UpdateSubData
        UpdateSubData subData = new UpdateSubData();
        subData.addressoffect = fullAddress;
        subData.bindatas = new ArrayList<>();
        
        // Add data bytes
        for (byte b : record.data) {
            subData.bindatas.add(b);
        }
        
        updateSubDatas.add(subData);
        
        // Log data content for debugging
        StringBuilder dataStr = new StringBuilder();
        for (byte b : record.data) {
            dataStr.append(String.format("%02X ", b & 0xFF));
        }
//        Log.d(TAG, "Data: " + dataStr.toString().trim());
    }
    
    /**
     * Apply BANK switching logic
     * This is specific to the SGN-168 firmware structure
     */
    private void applyBankSwitchingLogic() {
        Log.d(TAG, "=== Applying BANK Switching Logic ===");
        
        // Group data by memory banks
        List<UpdateSubData> bank0Data = new ArrayList<>();
        List<UpdateSubData> bank1Data = new ArrayList<>();
        List<UpdateSubData> otherData = new ArrayList<>();
        
        for (UpdateSubData data : updateSubDatas) {
            if (data.addressoffect >= 0x08000000 && data.addressoffect < 0x08020000) {
                // Bank 0 (0x08000000 - 0x0801FFFF)
                bank0Data.add(data);
            } else if (data.addressoffect >= 0x08020000 && data.addressoffect < 0x08040000) {
                // Bank 1 (0x08020000 - 0x0803FFFF)
                bank1Data.add(data);
            } else {
                // Other memory regions
                otherData.add(data);
            }
        }
        
        Log.d(TAG, "Bank 0 data blocks: " + bank0Data.size());
        Log.d(TAG, "Bank 1 data blocks: " + bank1Data.size());
        Log.d(TAG, "Other data blocks: " + otherData.size());
        
        // Reorganize data for optimal programming sequence
        List<UpdateSubData> reorganizedData = new ArrayList<>();
        
        // Add Bank 0 data first
        reorganizedData.addAll(bank0Data);
        
        // Add Bank 1 data
        reorganizedData.addAll(bank1Data);
        
        // Add other data
        reorganizedData.addAll(otherData);
        
        // Replace original data with reorganized data
        updateSubDatas.clear();
        updateSubDatas.addAll(reorganizedData);
        
        Log.d(TAG, "BANK switching logic applied. Total blocks: " + updateSubDatas.size());
    }
    
    /**
     * Get parsed update data
     */
    public static List<UpdateSubData> getUpdateSubDatas() {
        return updateSubDatas;
    }
    
    /**
     * Get original hex strings
     */
    public static List<String> getUpdateHexStrings() {
        return updateHexStrings;
    }

    /**
     * Get human-readable record type string
     */
    private String getRecordTypeString(int recordType) {
        switch (recordType) {
            case 0x00: return "Data";
            case 0x01: return "End of File";
            case 0x02: return "Extended Segment Address";
            case 0x03: return "Start Segment Address";
            case 0x04: return "Extended Linear Address";
            case 0x05: return "Start Linear Address";
            default: return "Unknown";
        }
    }
    
    /**
     * Inner class for hex record structure
     */
    private static class HexRecord {
        int byteCount;
        int address;
        int recordType;
        byte[] data;
        int checksum;
        
        HexRecord(int byteCount, int address, int recordType, byte[] data, int checksum) {
            this.byteCount = byteCount;
            this.address = address;
            this.recordType = recordType;
            this.data = data;
            this.checksum = checksum;
        }
    }
}